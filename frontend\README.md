# Smart Shelf Life Optimizer Frontend

A modern React dashboard for visualizing and managing product shelf life and inventory.

## Features

- Interactive shelf life heatmap visualization
- Distribution suggestions with priority indicators
- Waste risk analysis with progress indicators
- CSV data import functionality
- Real-time data updates
- Responsive design for all devices

## Tech Stack

- React with TypeScript
- Material-UI for components and styling
- Chart.js for data visualization
- React Query for data fetching and caching
- Axios for API communication

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm start
   ```

3. Build for production:
   ```bash
   npm run build
   ```

## Project Structure

```
src/
├── components/          # React components
│   ├── Dashboard.tsx    # Main dashboard layout
│   ├── HeatmapChart.tsx # Shelf life visualization
│   ├── DistributionList.tsx # Distribution suggestions
│   ├── WasteRiskList.tsx # Waste risk analysis
│   └── ImportDialog.tsx # CSV import dialog
├── services/           # API services
│   └── api.ts         # API client and endpoints
├── types/             # TypeScript type definitions
│   └── index.ts       # Shared types
└── App.tsx            # Root component
```

## API Integration

The frontend communicates with the backend API at `http://localhost:8000/api/v1`. Make sure the backend server is running before starting the frontend.

## CSV Import Format

The application expects CSV files with the following columns:
- Item_No
- Lot_No
- Expiration_Date
- Quantity
- Last_Movement_Date

Example:
```csv
Item_No,Lot_No,Expiration_Date,Quantity,Last_Movement_Date
MILK001,LOT20240101,2024-01-15,100,2024-01-01
```

## Development

- The application uses React Query for data management
- Material-UI theme can be customized in `App.tsx`
- Chart.js configurations can be modified in `HeatmapChart.tsx`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request
