import pandas as pd
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from io import StringIO
from app.models.product import Product, Lot, InventoryItem, ProductCategory
from app.db.session import get_db

def parse_date(date_str: str) -> datetime:
    """Parse date string in various formats."""
    formats = ['%Y-%m-%d', '%m/%d/%Y', '%d/%m/%Y', '%Y/%m/%d']
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue
    raise ValueError(f"Unable to parse date: {date_str}")

def import_inventory_data(db: Session, csv_data: str, column_mapping: Dict[str, str] = None) -> Dict[str, Any]:
    """Import inventory data from CSV string with optional column mapping."""
    try:
        # Read CSV data
        df = pd.read_csv(StringIO(csv_data))
        print(f"Original CSV columns: {list(df.columns)}")
        print(f"DataFrame shape: {df.shape}")

        # Auto-detect ERP format and apply mapping if no explicit mapping provided
        if not column_mapping:
            # Check for specific ERP column patterns
            erp_mapping = {}

            # Map Item_No - look for various patterns
            item_patterns = ['no.', 'item', 'product', 'sku', 'code', 'part']
            for col in df.columns:
                col_lower = col.lower().strip()
                if any(pattern in col_lower for pattern in item_patterns):
                    erp_mapping[col] = 'Item_No'
                    print(f"Found Item_No candidate: '{col}'")
                    break

            # Map Quantity - prefer specific quantity columns
            quantity_patterns = [
                ('Quantity on Hand', 'quantity on hand'),
                ('Case Units on Hand', 'case units on hand'),
                ('Quantity', 'quantity'),
                ('Qty', 'qty'),
                ('Stock', 'stock'),
                ('Amount', 'amount')
            ]

            for col in df.columns:
                col_lower = col.lower().strip()
                for pattern_name, pattern in quantity_patterns:
                    if pattern in col_lower and 'expired' not in col_lower:
                        erp_mapping[col] = 'Quantity'
                        print(f"Found Quantity candidate: '{col}'")
                        break
                if col in erp_mapping:  # Break outer loop if found
                    break

            # If we detected a basic ERP format, use it
            if erp_mapping and 'Item_No' in erp_mapping.values():
                print(f"Auto-detected ERP format, applying mapping: {erp_mapping}")

                # Create a new DataFrame with only the mapped columns
                new_df_data = {}
                for original_col, target_col in erp_mapping.items():
                    if original_col in df.columns:
                        new_df_data[target_col] = df[original_col].copy()
                        print(f"Mapping column '{original_col}' to '{target_col}'")

                # Replace the DataFrame with only mapped columns
                df = pd.DataFrame(new_df_data)
                print(f"Created new DataFrame with columns: {list(df.columns)}")

                # Clear column_mapping since we've already applied it
                column_mapping = None

        # Apply column mapping if provided or detected
        if column_mapping:
            # Rename columns according to mapping
            df = df.rename(columns=column_mapping)
            print(f"Applied column mapping: {column_mapping}")

        # Skip strict validation - we'll generate missing columns automatically
        print("Skipping strict column validation - will generate missing columns with defaults")

        # Add default values for missing columns - always do this for any CSV
        required_columns = ['Item_No', 'Lot_No', 'Expiration_Date', 'Quantity', 'Last_Movement_Date']

        # Ensure we have Item_No (most critical)
        if 'Item_No' not in df.columns:
            # Try to find any column that could be an item identifier
            for col in df.columns:
                col_lower = col.lower().strip()
                if any(pattern in col_lower for pattern in ['no.', 'item', 'product', 'sku', 'code', 'id']):
                    df['Item_No'] = df[col].astype(str)
                    print(f"Using '{col}' as Item_No")
                    break
            else:
                # Generate sequential item numbers if no suitable column found
                df['Item_No'] = [f"ITEM_{i+1:03d}" for i in range(len(df))]
                print("Generated sequential Item_No values")

        # Ensure we have Quantity
        if 'Quantity' not in df.columns:
            # Try to find any numeric column that could be quantity
            for col in df.columns:
                col_lower = col.lower().strip()
                if any(pattern in col_lower for pattern in ['qty', 'quantity', 'amount', 'stock', 'count']):
                    try:
                        # Test if the column is numeric
                        pd.to_numeric(df[col], errors='coerce')
                        df['Quantity'] = df[col]
                        print(f"Using '{col}' as Quantity")
                        break
                    except:
                        continue
            else:
                # Default quantity of 1 if no suitable column found
                df['Quantity'] = 1
                print("Set default quantity to 1")

        # Generate missing required columns with defaults
        if 'Lot_No' not in df.columns:
            df['Lot_No'] = df['Item_No'].astype(str) + '_LOT_' + datetime.now().strftime('%Y%m%d')
            print("Added default lot numbers")

        if 'Expiration_Date' not in df.columns:
            # Default to 30 days from now
            from datetime import timedelta
            default_expiry = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
            df['Expiration_Date'] = default_expiry
            print(f"Added default expiration date: {default_expiry}")

        if 'Last_Movement_Date' not in df.columns:
            df['Last_Movement_Date'] = datetime.now().strftime('%Y-%m-%d')
            print("Added default last movement date")

        print(f"Final DataFrame columns: {list(df.columns)}")

        # Process each row
        imported_items = []
        errors = []

        for index, row in df.iterrows():
            try:
                # Validate and clean data
                item_no = str(row['Item_No']).strip()
                if not item_no or item_no == 'nan':
                    errors.append({
                        "row": index + 1,
                        "error": "Missing or invalid Item_No"
                    })
                    continue

                # Handle quantity - convert to int safely
                try:
                    qty_raw = row['Quantity']
                    print(f"Row {index + 1}: Raw quantity value type: {type(qty_raw)}, value: {repr(qty_raw)}")

                    # Handle pandas Series or other complex objects
                    if hasattr(qty_raw, 'iloc'):
                        # It's a pandas Series, take the first value
                        qty_value = str(qty_raw.iloc[0]).strip()
                    else:
                        qty_value = str(qty_raw).strip()

                    print(f"Row {index + 1}: Processed quantity value: '{qty_value}'")

                    # Remove commas and handle various formats
                    qty_value = qty_value.replace(',', '').replace('$', '')

                    # Handle empty or invalid values
                    if not qty_value or qty_value.lower() in ['nan', 'null', '']:
                        quantity = 0
                    else:
                        quantity = int(float(qty_value))  # Convert via float first to handle decimals
                        if quantity < 0:
                            quantity = 0

                    print(f"Row {index + 1}: Final quantity: {quantity}")

                except (ValueError, TypeError) as e:
                    errors.append({
                        "row": index + 1,
                        "error": f"Invalid quantity value: '{row['Quantity']}' (type: {type(row['Quantity'])}) - {str(e)}"
                    })
                    continue

                # Create or update product
                product = db.query(Product).filter(Product.sku == item_no).first()
                if not product:
                    product = Product(
                        name=f"Product {item_no}",  # Default name if not provided
                        sku=item_no,
                        category=ProductCategory.OTHER,  # Default category
                        default_shelf_life_days=30,  # Default shelf life
                        min_shelf_life_days=7,  # Default minimum shelf life
                        optimal_storage_temp=4.0  # Default temperature
                    )
                    db.add(product)
                    db.flush()

                # Create or update lot
                lot = db.query(Lot).filter(Lot.lot_number == str(row['Lot_No'])).first()
                if not lot:
                    try:
                        production_date = parse_date(str(row['Last_Movement_Date']))
                        expiration_date = parse_date(str(row['Expiration_Date']))
                    except ValueError as e:
                        errors.append({
                            "row": index + 1,
                            "error": f"Date parsing error: {str(e)}"
                        })
                        continue

                    lot = Lot(
                        product_id=product.id,
                        lot_number=str(row['Lot_No']),
                        production_date=production_date,
                        expiration_date=expiration_date,
                        quantity=quantity
                    )
                    db.add(lot)
                    db.flush()

                # Create or update inventory item
                inventory_item = db.query(InventoryItem).filter(
                    InventoryItem.product_id == product.id,
                    InventoryItem.lot_id == lot.id
                ).first()

                if not inventory_item:
                    inventory_item = InventoryItem(
                        product_id=product.id,
                        lot_id=lot.id,
                        location="Default",  # Default location
                        quantity=quantity,
                        current_temp=4.0,  # Default temperature
                        status="normal"
                    )
                    db.add(inventory_item)

                imported_items.append({
                    "item_no": item_no,
                    "lot_no": str(row['Lot_No']),
                    "quantity": quantity
                })

            except Exception as e:
                errors.append({
                    "row": index + 1,
                    "error": str(e)
                })

        if errors:
            # Show first few errors for debugging
            error_summary = errors[:5]  # Show first 5 errors
            return {
                "success": False,
                "error": f"Errors occurred during import: {len(errors)} rows failed. First errors: {error_summary}",
                "errors": errors,
                "total_errors": len(errors)
            }

        # Commit changes
        db.commit()

        return {
            "success": True,
            "imported_items": imported_items,
            "message": f"Successfully imported {len(imported_items)} items"
        }

    except Exception as e:
        db.rollback()
        import traceback
        error_details = traceback.format_exc()
        print(f"Import error details: {error_details}")
        return {
            "success": False,
            "error": f"Import failed: {str(e)}",
            "details": error_details
        }