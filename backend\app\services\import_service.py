import pandas as pd
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from io import StringIO
from app.models.product import Product, Lot, InventoryItem, ProductCategory
from app.db.session import get_db

def parse_date(date_str: str) -> datetime:
    """Parse date string in various formats."""
    formats = ['%Y-%m-%d', '%m/%d/%Y', '%d/%m/%Y', '%Y/%m/%d']
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue
    raise ValueError(f"Unable to parse date: {date_str}")

def import_inventory_data(db: Session, csv_data: str, column_mapping: Dict[str, str] = None) -> Dict[str, Any]:
    """Import inventory data from CSV string with optional column mapping."""
    try:
        # Read CSV data
        df = pd.read_csv(StringIO(csv_data))

        # Auto-detect ERP format and apply mapping if no explicit mapping provided
        if not column_mapping:
            # Check for specific ERP column patterns
            erp_mapping = {}
            for col in df.columns:
                col_lower = col.lower().strip()
                if col == 'No.' or col_lower == 'no.':
                    erp_mapping[col] = 'Item_No'
                elif 'quantity on hand' in col_lower:
                    erp_mapping[col] = 'Quantity'
                elif 'case units on hand' in col_lower and 'Quantity' not in erp_mapping.values():
                    erp_mapping[col] = 'Quantity'

            # If we detected ERP format, use it
            if erp_mapping and 'Item_No' in erp_mapping.values() and 'Quantity' in erp_mapping.values():
                column_mapping = erp_mapping
                print(f"Auto-detected ERP format, applying mapping: {column_mapping}")

        # Apply column mapping if provided or detected
        if column_mapping:
            # Rename columns according to mapping
            df = df.rename(columns=column_mapping)
            print(f"Applied column mapping: {column_mapping}")

        # Validate required columns - but allow for defaults
        required_columns = ['Item_No', 'Lot_No', 'Expiration_Date', 'Quantity', 'Last_Movement_Date']
        missing_columns = [col for col in required_columns if col not in df.columns]

        # If we have column mapping, we can be more flexible
        if column_mapping:
            # Check if we can map the essential fields
            essential_fields = ['Item_No', 'Quantity']  # Minimum required
            mapped_targets = set(column_mapping.values())
            missing_essential = [field for field in essential_fields if field not in mapped_targets]

            if missing_essential:
                return {
                    "success": False,
                    "error": f"Missing essential columns: {', '.join(missing_essential)}. Found columns: {', '.join(df.columns)}",
                    "available_columns": list(df.columns),
                    "required_columns": required_columns,
                    "essential_columns": essential_fields
                }
        else:
            # Original strict validation for exact format
            if missing_columns:
                return {
                    "success": False,
                    "error": f"Missing required columns: {', '.join(missing_columns)}. Found columns: {', '.join(df.columns)}",
                    "available_columns": list(df.columns),
                    "required_columns": required_columns
                }

        # Add default values for missing columns when using column mapping
        if column_mapping:
            if 'Lot_No' not in df.columns:
                df['Lot_No'] = df['Item_No'].astype(str) + '_LOT_' + datetime.now().strftime('%Y%m%d')
                print("Added default lot numbers")

            if 'Expiration_Date' not in df.columns:
                # Default to 30 days from now
                from datetime import timedelta
                default_expiry = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
                df['Expiration_Date'] = default_expiry
                print(f"Added default expiration date: {default_expiry}")

            if 'Last_Movement_Date' not in df.columns:
                df['Last_Movement_Date'] = datetime.now().strftime('%Y-%m-%d')
                print("Added default last movement date")

        # Process each row
        imported_items = []
        errors = []

        for index, row in df.iterrows():
            try:
                # Create or update product
                product = db.query(Product).filter(Product.sku == str(row['Item_No'])).first()
                if not product:
                    product = Product(
                        name=f"Product {row['Item_No']}",  # Default name if not provided
                        sku=str(row['Item_No']),
                        category=ProductCategory.OTHER,  # Default category
                        default_shelf_life_days=30,  # Default shelf life
                        min_shelf_life_days=7,  # Default minimum shelf life
                        optimal_storage_temp=4.0  # Default temperature
                    )
                    db.add(product)
                    db.flush()

                # Create or update lot
                lot = db.query(Lot).filter(Lot.lot_number == str(row['Lot_No'])).first()
                if not lot:
                    try:
                        production_date = parse_date(str(row['Last_Movement_Date']))
                        expiration_date = parse_date(str(row['Expiration_Date']))
                    except ValueError as e:
                        errors.append({
                            "row": index + 1,
                            "error": f"Date parsing error: {str(e)}"
                        })
                        continue

                    lot = Lot(
                        product_id=product.id,
                        lot_number=str(row['Lot_No']),
                        production_date=production_date,
                        expiration_date=expiration_date,
                        quantity=int(row['Quantity'])
                    )
                    db.add(lot)
                    db.flush()

                # Create or update inventory item
                inventory_item = db.query(InventoryItem).filter(
                    InventoryItem.product_id == product.id,
                    InventoryItem.lot_id == lot.id
                ).first()

                if not inventory_item:
                    inventory_item = InventoryItem(
                        product_id=product.id,
                        lot_id=lot.id,
                        location="Default",  # Default location
                        quantity=int(row['Quantity']),
                        current_temp=4.0,  # Default temperature
                        status="normal"
                    )
                    db.add(inventory_item)

                imported_items.append({
                    "item_no": str(row['Item_No']),
                    "lot_no": str(row['Lot_No']),
                    "quantity": int(row['Quantity'])
                })

            except Exception as e:
                errors.append({
                    "row": index + 1,
                    "error": str(e)
                })

        if errors:
            return {
                "success": False,
                "error": f"Errors occurred during import: {len(errors)} rows failed",
                "errors": errors
            }

        # Commit changes
        db.commit()

        return {
            "success": True,
            "imported_items": imported_items,
            "message": f"Successfully imported {len(imported_items)} items"
        }

    except Exception as e:
        db.rollback()
        import traceback
        error_details = traceback.format_exc()
        print(f"Import error details: {error_details}")
        return {
            "success": False,
            "error": f"Import failed: {str(e)}",
            "details": error_details
        }