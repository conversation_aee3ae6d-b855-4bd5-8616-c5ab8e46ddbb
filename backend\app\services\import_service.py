import pandas as pd
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from io import StringIO
from app.models.product import Product, Lot, InventoryItem, ProductCategory
from app.db.session import get_db

def parse_date(date_str: str) -> datetime:
    """Parse date string in various formats."""
    formats = ['%Y-%m-%d', '%m/%d/%Y', '%d/%m/%Y', '%Y/%m/%d']
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue
    raise ValueError(f"Unable to parse date: {date_str}")

def import_inventory_data(db: Session, csv_data: str, column_mapping: Dict[str, str] = None) -> Dict[str, Any]:
    """Universal CSV import that works with any format."""
    try:
        # Read CSV data with flexible parsing
        df = pd.read_csv(StringIO(csv_data))

        # Remove completely empty rows and columns
        df = df.dropna(how='all').dropna(axis=1, how='all')

        if df.empty:
            return {
                "success": False,
                "error": "CSV file is empty or contains no valid data"
            }

        print(f"📊 Processing CSV: {df.shape[0]} rows, {df.shape[1]} columns")
        print(f"📋 Columns: {list(df.columns)}")

        # Universal column detection and mapping
        final_df = create_universal_mapping(df, column_mapping)

        if final_df is None:
            return {
                "success": False,
                "error": "Could not identify any suitable data columns in the CSV"
            }

        print(f"✅ Final DataFrame ready: {final_df.shape[0]} rows")
        print(f"📋 Final columns: {list(final_df.columns)}")

        # Process and import the data
        return process_and_import_data(db, final_df)

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"❌ Import error: {error_details}")
        return {
            "success": False,
            "error": f"Failed to process CSV: {str(e)}",
            "details": error_details
        }


def create_universal_mapping(df: pd.DataFrame, column_mapping: Dict[str, str] = None) -> pd.DataFrame:
    """Create a universal mapping for any CSV format."""

    # If explicit mapping provided, use it
    if column_mapping:
        mapped_df = df.rename(columns=column_mapping)
        return ensure_required_columns(mapped_df)

    # Auto-detect the best columns for each required field
    result_df = pd.DataFrame()

    # 1. Find Item/Product identifier
    item_col = find_best_column(df, [
        'no', 'item', 'product', 'sku', 'code', 'part', 'id', 'name'
    ])

    if item_col:
        result_df['Item_No'] = df[item_col].astype(str).str.strip()
        print(f"🏷️  Using '{item_col}' as Item_No")
    else:
        # Generate sequential IDs
        result_df['Item_No'] = [f"ITEM_{i+1:04d}" for i in range(len(df))]
        print("🏷️  Generated sequential Item_No values")

    # 2. Find Quantity
    qty_col = find_best_numeric_column(df, [
        'quantity', 'qty', 'amount', 'stock', 'count', 'units', 'hand'
    ])

    if qty_col:
        result_df['Quantity'] = clean_numeric_column(df[qty_col])
        print(f"📦 Using '{qty_col}' as Quantity")
    else:
        result_df['Quantity'] = 1
        print("📦 Set default Quantity to 1")

    # 3. Find or generate other required fields
    result_df = ensure_required_columns(result_df)

    return result_df


def find_best_column(df: pd.DataFrame, patterns: list) -> str:
    """Find the best matching column based on patterns."""
    for pattern in patterns:
        for col in df.columns:
            if pattern.lower() in col.lower():
                # Check if column has reasonable data
                non_null_count = df[col].count()
                if non_null_count > 0:
                    return col
    return None


def find_best_numeric_column(df: pd.DataFrame, patterns: list) -> str:
    """Find the best numeric column matching patterns."""
    candidates = []

    for pattern in patterns:
        for col in df.columns:
            if pattern.lower() in col.lower():
                # Test if column is numeric
                try:
                    numeric_data = pd.to_numeric(df[col], errors='coerce')
                    non_null_count = numeric_data.count()
                    if non_null_count > 0:
                        candidates.append((col, non_null_count))
                except:
                    continue

    # Return the column with the most valid numeric data
    if candidates:
        return max(candidates, key=lambda x: x[1])[0]

    return None


def clean_numeric_column(series: pd.Series) -> pd.Series:
    """Clean and convert a column to numeric values."""
    # Convert to string first, then clean
    cleaned = series.astype(str).str.replace(',', '').str.replace('$', '').str.strip()

    # Convert to numeric, replacing errors with 0
    numeric = pd.to_numeric(cleaned, errors='coerce').fillna(0)

    # Ensure non-negative integers
    return numeric.abs().astype(int)


def ensure_required_columns(df: pd.DataFrame) -> pd.DataFrame:
    """Ensure all required columns exist with sensible defaults."""
    from datetime import timedelta

    # Generate Lot_No if missing
    if 'Lot_No' not in df.columns:
        timestamp = datetime.now().strftime('%Y%m%d')
        df['Lot_No'] = df['Item_No'].astype(str) + f'_LOT_{timestamp}'

    # Generate Expiration_Date if missing
    if 'Expiration_Date' not in df.columns:
        default_expiry = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
        df['Expiration_Date'] = default_expiry

    # Generate Last_Movement_Date if missing
    if 'Last_Movement_Date' not in df.columns:
        df['Last_Movement_Date'] = datetime.now().strftime('%Y-%m-%d')

    return df


def process_and_import_data(db: Session, df: pd.DataFrame) -> Dict[str, Any]:
    """Process the cleaned DataFrame and import into database."""
    try:
        imported_items = []
        errors = []

        print(f"🔄 Processing {len(df)} rows for import...")

        for index, row in df.iterrows():
            try:
                # Validate and clean data
                item_no = str(row['Item_No']).strip()
                if not item_no or item_no.lower() in ['nan', 'null', '']:
                    errors.append({
                        "row": index + 1,
                        "error": "Missing or invalid Item_No"
                    })
                    continue

                # Handle quantity safely
                try:
                    quantity = int(row['Quantity'])
                    if quantity < 0:
                        quantity = 0
                except (ValueError, TypeError):
                    quantity = 1
                    print(f"⚠️  Row {index + 1}: Using default quantity 1")

                # Create or update product
                product = db.query(Product).filter(Product.sku == item_no).first()
                if not product:
                    product = Product(
                        name=f"Product {item_no}",
                        sku=item_no,
                        category=ProductCategory.OTHER,
                        default_shelf_life_days=30,
                        min_shelf_life_days=7,
                        optimal_storage_temp=4.0
                    )
                    db.add(product)
                    db.flush()

                # Create or update lot
                lot_no = str(row['Lot_No']).strip()
                lot = db.query(Lot).filter(Lot.lot_number == lot_no).first()
                if not lot:
                    try:
                        production_date = parse_date(str(row['Last_Movement_Date']))
                        expiration_date = parse_date(str(row['Expiration_Date']))
                    except ValueError as e:
                        # Use defaults if date parsing fails
                        from datetime import timedelta
                        production_date = datetime.now()
                        expiration_date = datetime.now() + timedelta(days=30)
                        print(f"⚠️  Row {index + 1}: Using default dates due to parsing error")

                    lot = Lot(
                        product_id=product.id,
                        lot_number=lot_no,
                        production_date=production_date,
                        expiration_date=expiration_date,
                        quantity=quantity
                    )
                    db.add(lot)
                    db.flush()

                # Create or update inventory item
                inventory_item = db.query(InventoryItem).filter(
                    InventoryItem.product_id == product.id,
                    InventoryItem.lot_id == lot.id
                ).first()

                if not inventory_item:
                    inventory_item = InventoryItem(
                        product_id=product.id,
                        lot_id=lot.id,
                        location="Default",
                        quantity=quantity,
                        current_temp=4.0,
                        status="normal"
                    )
                    db.add(inventory_item)
                else:
                    # Update existing inventory
                    inventory_item.quantity = quantity

                imported_items.append({
                    "item_no": item_no,
                    "lot_no": lot_no,
                    "quantity": quantity
                })

            except Exception as e:
                errors.append({
                    "row": index + 1,
                    "error": f"Processing error: {str(e)}"
                })
                print(f"❌ Row {index + 1} error: {str(e)}")

        # Commit all changes
        db.commit()

        success_count = len(imported_items)
        error_count = len(errors)

        print(f"✅ Import completed: {success_count} successful, {error_count} errors")

        if error_count > 0 and success_count == 0:
            return {
                "success": False,
                "error": f"All {error_count} rows failed to import",
                "errors": errors[:10]  # Show first 10 errors
            }

        return {
            "success": True,
            "imported_count": success_count,
            "error_count": error_count,
            "imported_items": imported_items[:10],  # Show first 10 items
            "message": f"Successfully imported {success_count} items" +
                      (f" ({error_count} rows had errors)" if error_count > 0 else "")
        }

    except Exception as e:
        db.rollback()
        import traceback
        error_details = traceback.format_exc()
        print(f"❌ Database error: {error_details}")
        return {
            "success": False,
            "error": f"Database error: {str(e)}",
            "details": error_details
        }