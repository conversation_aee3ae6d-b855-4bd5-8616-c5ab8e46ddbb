import pandas as pd
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from app.models.product import Product, Lot, InventoryItem, ProductCategory
from app.db.session import get_db

def parse_date(date_str: str) -> datetime:
    """Parse date string in various formats."""
    formats = ['%Y-%m-%d', '%m/%d/%Y', '%d/%m/%Y', '%Y/%m/%d']
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue
    raise ValueError(f"Unable to parse date: {date_str}")

def import_inventory_data(db: Session, csv_data: str) -> Dict[str, Any]:
    """Import inventory data from CSV string."""
    try:
        # Read CSV data
        df = pd.read_csv(pd.StringIO(csv_data))
        
        # Validate required columns
        required_columns = ['Item_No', 'Lot_No', 'Expiration_Date', 'Quantity', 'Last_Movement_Date']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return {
                "success": False,
                "error": f"Missing required columns: {', '.join(missing_columns)}"
            }
        
        # Process each row
        imported_items = []
        errors = []
        
        for index, row in df.iterrows():
            try:
                # Create or update product
                product = db.query(Product).filter(Product.sku == str(row['Item_No'])).first()
                if not product:
                    product = Product(
                        name=f"Product {row['Item_No']}",  # Default name if not provided
                        sku=str(row['Item_No']),
                        category=ProductCategory.OTHER,  # Default category
                        default_shelf_life_days=30,  # Default shelf life
                        min_shelf_life_days=7,  # Default minimum shelf life
                        optimal_storage_temp=4.0  # Default temperature
                    )
                    db.add(product)
                    db.flush()
                
                # Create or update lot
                lot = db.query(Lot).filter(Lot.lot_number == str(row['Lot_No'])).first()
                if not lot:
                    try:
                        production_date = parse_date(str(row['Last_Movement_Date']))
                        expiration_date = parse_date(str(row['Expiration_Date']))
                    except ValueError as e:
                        errors.append({
                            "row": index + 1,
                            "error": f"Date parsing error: {str(e)}"
                        })
                        continue

                    lot = Lot(
                        product_id=product.id,
                        lot_number=str(row['Lot_No']),
                        production_date=production_date,
                        expiration_date=expiration_date,
                        quantity=int(row['Quantity'])
                    )
                    db.add(lot)
                    db.flush()
                
                # Create or update inventory item
                inventory_item = db.query(InventoryItem).filter(
                    InventoryItem.product_id == product.id,
                    InventoryItem.lot_id == lot.id
                ).first()
                
                if not inventory_item:
                    inventory_item = InventoryItem(
                        product_id=product.id,
                        lot_id=lot.id,
                        location="Default",  # Default location
                        quantity=int(row['Quantity']),
                        current_temp=4.0,  # Default temperature
                        status="normal"
                    )
                    db.add(inventory_item)
                
                imported_items.append({
                    "item_no": str(row['Item_No']),
                    "lot_no": str(row['Lot_No']),
                    "quantity": int(row['Quantity'])
                })
                
            except Exception as e:
                errors.append({
                    "row": index + 1,
                    "error": str(e)
                })
        
        if errors:
            return {
                "success": False,
                "error": f"Errors occurred during import: {len(errors)} rows failed",
                "errors": errors
            }
        
        # Commit changes
        db.commit()
        
        return {
            "success": True,
            "imported_items": imported_items,
            "message": f"Successfully imported {len(imported_items)} items"
        }
        
    except Exception as e:
        db.rollback()
        return {
            "success": False,
            "error": f"Import failed: {str(e)}"
        } 