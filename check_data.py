#!/usr/bin/env python3
"""
Quick script to check if data was imported
"""
import requests
import json

def check_imported_data():
    print("Checking if data was imported...")
    
    try:
        # Check heatmap data
        response = requests.get("http://localhost:8000/api/v1/analytics/heatmap", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Heatmap API working. Data points: {len(data)}")
            if data:
                print(f"Sample data: {data[0]}")
            else:
                print("❌ No data in heatmap")
        else:
            print(f"❌ Heatmap API failed: {response.status_code}")
            
        # Check distribution data
        response = requests.get("http://localhost:8000/api/v1/analytics/distribution", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Distribution API working. Categories: {len(data)}")
            if data:
                print(f"Sample data: {data[0]}")
            else:
                print("❌ No data in distribution")
        else:
            print(f"❌ Distribution API failed: {response.status_code}")
            
        # Check waste risk data
        response = requests.get("http://localhost:8000/api/v1/analytics/waste-risk", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Waste Risk API working. Items: {len(data)}")
            if data:
                print(f"Sample data: {data[0]}")
            else:
                print("❌ No data in waste risk")
        else:
            print(f"❌ Waste Risk API failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error checking data: {e}")

if __name__ == "__main__":
    check_imported_data()
