from datetime import datetime, timedelta
import random
from typing import List, Dict
from app.models.inventory import InventoryItem
from app.models.product import Product
from app.models.lot import Lot

# Fun product names with puns
PRODUCT_NAMES = [
    "Moo-ve Over Milk",
    "Egg-cellent Eggs",
    "Bread Winner Loaf",
    "Cheese Please!",
    "Yogurt to be Kidding",
    "Butter Believe It",
    "Hamazing Ham",
    "Bacon Me Crazy",
    "Chicken Out Wings",
    "Tuna Fishy Business"
]

# Fun location names
LOCATIONS = [
    "The Cool Zone",
    "Frosty's Corner",
    "Chill Out Chamber",
    "Ice Box Inn",
    "Freezer Frenzy",
    "Cold Storage Castle",
    "Arctic Avenue",
    "Frostbite Factory",
    "Snowflake Station",
    "Winter Wonder Warehouse"
]

def generate_fun_data(num_items: int = 50) -> List[Dict]:
    """Generate fun but realistic inventory data."""
    items = []
    today = datetime.now()
    
    for _ in range(num_items):
        # Random product selection
        product_name = random.choice(PRODUCT_NAMES)
        location = random.choice(LOCATIONS)
        
        # Generate random dates
        days_to_expiry = random.randint(1, 30)
        expiration_date = today + timedelta(days=days_to_expiry)
        last_movement = today - timedelta(days=random.randint(0, 7))
        
        # Generate random quantities
        quantity = random.randint(10, 1000)
        
        # Generate fun lot numbers
        lot_prefix = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ', k=3))
        lot_number = f"{lot_prefix}-{random.randint(1000, 9999)}"
        
        # Calculate risk level (higher for items closer to expiry)
        risk_level = min(100, max(0, (30 - days_to_expiry) * 3.33))
        
        # Generate fun status messages
        status_messages = [
            "Feeling Fresh!",
            "Getting Anxious",
            "Time to Move!",
            "Still Cool",
            "Chillin' Like a Villain",
            "Running Out of Time!",
            "Fresh as a Daisy",
            "Getting Nervous",
            "Cool as a Cucumber",
            "Time's Ticking!"
        ]
        status = random.choice(status_messages)
        
        items.append({
            "item_no": f"ITEM-{random.randint(1000, 9999)}",
            "lot_no": lot_number,
            "product_name": product_name,
            "location": location,
            "quantity": quantity,
            "expiration_date": expiration_date.isoformat(),
            "last_movement_date": last_movement.isoformat(),
            "risk_level": risk_level,
            "status": status,
            "current_temp": round(random.uniform(2.0, 8.0), 1),
            "optimal_temp": round(random.uniform(2.0, 8.0), 1)
        })
    
    return items

def generate_distribution_suggestions(items: List[Dict]) -> List[Dict]:
    """Generate fun distribution suggestions based on the inventory data."""
    suggestions = []
    
    for item in items:
        # Calculate urgency score based on risk level and quantity
        urgency_score = min(100, item["risk_level"] + (item["quantity"] / 20))
        
        # Fun suggestion messages
        suggestion_messages = [
            "Move it or Lose it!",
            "Time to Shake Things Up!",
            "Let's Get Moving!",
            "Destination: Anywhere but Here!",
            "New Home Needed!",
            "Ready for a Change!",
            "Time for a Road Trip!",
            "Let's Hit the Road!",
            "New Horizons Await!",
            "Time to Travel!"
        ]
        
        # Determine priority based on urgency
        if urgency_score >= 80:
            suggestion = "Prioritize"
            message = "🚨 " + random.choice(suggestion_messages)
        elif urgency_score >= 50:
            suggestion = "Normal"
            message = "⚠️ " + random.choice(suggestion_messages)
        else:
            suggestion = "Low Priority"
            message = "✅ " + random.choice(suggestion_messages)
        
        suggestions.append({
            "id": random.randint(1000, 9999),
            "product_name": item["product_name"],
            "location": item["location"],
            "quantity": item["quantity"],
            "days_to_expiry": (datetime.fromisoformat(item["expiration_date"]) - datetime.now()).days,
            "urgency_score": round(urgency_score, 1),
            "suggestion": suggestion,
            "message": message
        })
    
    return suggestions

def generate_waste_risk_analysis(items: List[Dict]) -> List[Dict]:
    """Generate fun waste risk analysis based on the inventory data."""
    waste_risks = []
    
    for item in items:
        # Calculate daily consumption (random but realistic)
        daily_consumption = round(random.uniform(5, 50), 2)
        
        # Calculate waste risk based on quantity, days to expiry, and consumption
        days_to_expiry = (datetime.fromisoformat(item["expiration_date"]) - datetime.now()).days
        waste_risk = min(100, max(0, (item["quantity"] / (daily_consumption * days_to_expiry)) * 100))
        
        # Fun risk status messages
        risk_messages = {
            "High Risk": ["🚨 Code Red!", "🚨 Emergency!", "🚨 Critical!"],
            "Medium Risk": ["⚠️ Watch Out!", "⚠️ Be Careful!", "⚠️ Heads Up!"],
            "Low Risk": ["✅ All Good!", "✅ No Worries!", "✅ Safe and Sound!"]
        }
        
        # Determine risk status
        if waste_risk >= 70:
            status = "High Risk"
        elif waste_risk >= 40:
            status = "Medium Risk"
        else:
            status = "Low Risk"
        
        message = random.choice(risk_messages[status])
        
        waste_risks.append({
            "id": random.randint(1000, 9999),
            "product_name": item["product_name"],
            "location": item["location"],
            "quantity": item["quantity"],
            "days_to_expiry": days_to_expiry,
            "daily_consumption": daily_consumption,
            "waste_risk": round(waste_risk, 1),
            "status": status,
            "message": message
        })
    
    return waste_risks 