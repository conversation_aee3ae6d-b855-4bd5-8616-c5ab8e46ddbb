import React from 'react';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    Filler,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { useQuery } from 'react-query';
import { getHeatmapData } from '../services/api';
import { HeatmapItem } from '../types';
import { Box, CircularProgress, Typography } from '@mui/material';

ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    Filler
);

export const HeatmapChart: React.FC = () => {
    const { data, isLoading, error } = useQuery<HeatmapItem[]>('heatmap', getHeatmapData);

    if (isLoading) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return <div>Error loading heatmap data</div>;
    }

    if (!data || data.length === 0) {
        return (
            <Box sx={{ p: 4, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                <Typography variant="h6" color="text.secondary" gutterBottom>
                    📊 No Inventory Data
                </Typography>
                <Typography variant="body2" color="text.secondary">
                    Import your inventory CSV file to see the shelf life heatmap
                </Typography>
            </Box>
        );
    }

    // Group data by location
    const locations = Array.from(new Set(data.map(item => item.location)));
    const datasets = locations.map(location => {
        const locationData = data.filter(item => item.location === location);
        return {
            label: location,
            data: locationData.map(item => item.risk_level),
            borderColor: getColorForRiskLevel(locationData[0]?.risk_level || 0),
            backgroundColor: getColorForRiskLevel(locationData[0]?.risk_level || 0, 0.2),
            fill: true,
        };
    });

    const chartData = {
        labels: data.map(item => item.product_name),
        datasets,
    };

    const options = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top' as const,
            },
            title: {
                display: true,
                text: 'Shelf Life Risk Levels by Location',
            },
        },
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                title: {
                    display: true,
                    text: 'Risk Level',
                },
            },
        },
    };

    return <Line data={chartData} options={options} />;
};

const getColorForRiskLevel = (riskLevel: number, alpha: number = 1): string => {
    if (riskLevel >= 80) return `rgba(255, 0, 0, ${alpha})`; // Red for high risk
    if (riskLevel >= 60) return `rgba(255, 165, 0, ${alpha})`; // Orange for medium-high risk
    if (riskLevel >= 40) return `rgba(255, 255, 0, ${alpha})`; // Yellow for medium risk
    if (riskLevel >= 20) return `rgba(144, 238, 144, ${alpha})`; // Light green for low-medium risk
    return `rgba(0, 255, 0, ${alpha})`; // Green for low risk
};