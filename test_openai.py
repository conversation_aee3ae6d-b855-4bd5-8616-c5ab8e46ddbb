#!/usr/bin/env python3
"""
Test OpenAI API connection
"""
import requests
import json

# Your OpenAI API key
OPENAI_API_KEY = '********************************************************************************************************************************************************************'

def test_openai_connection():
    """Test OpenAI API connection."""
    print("🧪 Testing OpenAI API connection...")
    
    headers = {
        'Authorization': f'Bearer {OPENAI_API_KEY}',
        'Content-Type': 'application/json'
    }
    
    data = {
        "model": "gpt-4o-mini",
        "messages": [
            {"role": "user", "content": "Say hello and confirm you're working!"}
        ],
        "max_tokens": 100
    }
    
    try:
        print("📡 Making API call...")
        response = requests.post(
            'https://api.openai.com/v1/chat/completions',
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result['choices'][0]['message']['content']
            print(f"✅ SUCCESS! OpenAI Response: {ai_response}")
            return True
        else:
            print(f"❌ ERROR: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

if __name__ == '__main__':
    test_openai_connection()
