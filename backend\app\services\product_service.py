from typing import List, Optional
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from app.models.product import Product, ProductCategory
from app.schemas.product import ProductCreate, ProductUpdate

def create_product(db: Session, product: ProductCreate) -> Product:
    db_product = Product(
        name=product.name,
        category=product.category,
        shelf_life_days=product.shelf_life_days,
        storage_conditions=product.storage_conditions,
        production_date=product.production_date,
        expiration_date=product.expiration_date,
        quantity=product.quantity,
        unit=product.unit
    )
    db.add(db_product)
    db.commit()
    db.refresh(db_product)
    return db_product

def get_product(db: Session, product_id: int) -> Optional[Product]:
    return db.query(Product).filter(Product.id == product_id).first()

def get_products(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    category: Optional[ProductCategory] = None
) -> List[Product]:
    query = db.query(Product)
    if category:
        query = query.filter(Product.category == category)
    return query.offset(skip).limit(limit).all()

def update_product(
    db: Session,
    product_id: int,
    product: ProductUpdate
) -> Optional[Product]:
    db_product = get_product(db=db, product_id=product_id)
    if db_product:
        update_data = product.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_product, key, value)
        db.commit()
        db.refresh(db_product)
    return db_product

def delete_product(db: Session, product_id: int) -> bool:
    db_product = get_product(db=db, product_id=product_id)
    if db_product:
        db.delete(db_product)
        db.commit()
        return True
    return False

def get_at_risk_products(
    db: Session,
    days_threshold: int = 7
) -> List[Product]:
    today = datetime.utcnow()
    threshold_date = today + timedelta(days=days_threshold)
    return db.query(Product).filter(
        Product.expiration_date <= threshold_date,
        Product.expiration_date > today
    ).all() 