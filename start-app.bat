@echo off
echo Starting Smart Shelf Life Optimizer...

REM Check if Docker is installed
where docker >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

REM Check if Docker is running
docker info >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Docker is not running. Please start Docker Desktop.
    pause
    exit /b 1
)

REM Build and start the containers
echo Building and starting containers...
docker-compose up --build -d

REM Wait for services to start
echo Waiting for services to start...
timeout /t 10 /nobreak

REM Open the application in the default browser
start http://localhost

echo Application is running!
echo Frontend: http://localhost
echo Backend API: http://localhost:8000
echo API Documentation: http://localhost:8000/docs
echo.
echo Press Ctrl+C to stop the application
echo.

REM Keep the window open and show logs
docker-compose logs -f 