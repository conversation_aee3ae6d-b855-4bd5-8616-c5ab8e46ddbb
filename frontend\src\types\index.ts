export interface Product {
    id: number;
    name: string;
    sku: string;
    category: string;
    default_shelf_life_days: number;
    min_shelf_life_days: number;
    optimal_storage_temp: number;
}

export interface HeatmapItem {
    id: number;
    product_name: string;
    location: string;
    quantity: number;
    days_to_expiry: number;
    risk_level: number;
    current_temp: number;
    optimal_temp: number;
}

export interface DistributionSuggestion {
    id: number;
    product_name: string;
    sku?: string;
    location: string;
    quantity: number;
    days_to_expiry: number;
    urgency_score: number;
    priority?: 'Critical' | 'High' | 'Medium' | 'Low';
    action?: string;
    action_type?: string;
    temp_deviation?: number;
    estimated_value?: number;
    potential_loss?: number;
    expiry_date?: string;
    suggestion?: 'Prioritize' | 'Normal' | 'Low Priority';
}

export interface WasteRisk {
    id: number;
    product_name: string;
    sku?: string;
    category?: string;
    location: string;
    quantity: number;
    days_to_expiry: number;
    expiry_date?: string;
    waste_risk: number;
    status: 'Critical Risk' | 'High Risk' | 'Medium Risk' | 'Low Risk' | 'Minimal Risk';
    recommendation?: string;
    color?: string;
    risk_factors?: {
        expiry_risk: number;
        quantity_risk: number;
        temperature_risk: number;
        category_risk: number;
    };
    temp_deviation?: number;
    current_temp?: number;
    optimal_temp?: number;
    total_value?: number;
    expected_waste_value?: number;
    waste_probability?: number;
    action_deadline?: string;
    daily_consumption?: number;
}

export interface ImportResult {
    success: boolean;
    imported_items?: Array<{
        item_no: string;
        lot_no: string;
        quantity: number;
    }>;
    errors?: Array<{
        row: Record<string, any>;
        error: string;
    }>;
    error?: string;
}