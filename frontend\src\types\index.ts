export interface Product {
    id: number;
    name: string;
    sku: string;
    category: string;
    default_shelf_life_days: number;
    min_shelf_life_days: number;
    optimal_storage_temp: number;
}

export interface HeatmapItem {
    id: number;
    product_name: string;
    location: string;
    quantity: number;
    days_to_expiry: number;
    risk_level: number;
    current_temp: number;
    optimal_temp: number;
}

export interface DistributionSuggestion {
    id: number;
    product_name: string;
    location: string;
    quantity: number;
    days_to_expiry: number;
    urgency_score: number;
    suggestion: 'Prioritize' | 'Normal' | 'Low Priority';
}

export interface WasteRisk {
    id: number;
    product_name: string;
    location: string;
    quantity: number;
    days_to_expiry: number;
    daily_consumption: number;
    waste_risk: number;
    status: 'High Risk' | 'Medium Risk' | 'Low Risk';
}

export interface ImportResult {
    success: boolean;
    imported_items?: Array<{
        item_no: string;
        lot_no: string;
        quantity: number;
    }>;
    errors?: Array<{
        row: Record<string, any>;
        error: string;
    }>;
    error?: string;
} 