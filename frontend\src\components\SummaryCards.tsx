import React from 'react';
import {
    <PERSON><PERSON>,
    Card,
    CardContent,
    Typography,
    Box,
    CircularProgress,
    Chip,
} from '@mui/material';
import {
    TrendingUp,
    Warning,
    CheckCircle,
    AttachMoney,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import { getDistributionSuggestions, getWasteRiskAnalysis } from '../services/api';

interface SummaryCardProps {
    title: string;
    value: string | number;
    subtitle?: string;
    icon: React.ReactNode;
    color: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
    trend?: {
        value: number;
        isPositive: boolean;
    };
}

const SummaryCard: React.FC<SummaryCardProps> = ({ title, value, subtitle, icon, color, trend }) => (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <CardContent sx={{ flex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Box sx={{ color: `${color}.main` }}>
                    {icon}
                </Box>
                {trend && (
                    <Chip
                        label={`${trend.isPositive ? '+' : ''}${trend.value}%`}
                        color={trend.isPositive ? 'success' : 'error'}
                        size="small"
                        variant="outlined"
                    />
                )}
            </Box>
            <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', color: `${color}.main` }}>
                {value}
            </Typography>
            <Typography variant="h6" color="text.primary" gutterBottom>
                {title}
            </Typography>
            {subtitle && (
                <Typography variant="body2" color="text.secondary">
                    {subtitle}
                </Typography>
            )}
        </CardContent>
    </Card>
);

export const SummaryCards: React.FC = () => {
    const { data: distributionData, isLoading: distributionLoading } = useQuery(
        'distribution',
        getDistributionSuggestions
    );

    const { data: wasteRiskData, isLoading: wasteRiskLoading } = useQuery(
        'wasteRisk',
        getWasteRiskAnalysis
    );

    if (distributionLoading || wasteRiskLoading) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" height="200px">
                <CircularProgress />
            </Box>
        );
    }

    // Calculate summary metrics
    const totalItems = wasteRiskData?.length || 0;
    const criticalItems = distributionData?.filter(item => 
        item.priority === 'Critical' || item.urgency_score > 80
    ).length || 0;
    
    const highRiskItems = wasteRiskData?.filter(item => 
        item.status === 'Critical Risk' || item.status === 'High Risk'
    ).length || 0;

    const totalPotentialLoss = distributionData?.reduce((sum, item) => 
        sum + (item.potential_loss || 0), 0
    ) || 0;

    const totalWasteValue = wasteRiskData?.reduce((sum, item) => 
        sum + (item.expected_waste_value || 0), 0
    ) || 0;

    const averageRisk = wasteRiskData?.length > 0 
        ? wasteRiskData.reduce((sum, item) => sum + (item.waste_risk || 0), 0) / wasteRiskData.length
        : 0;

    const expiringSoon = distributionData?.filter(item => 
        item.days_to_expiry <= 3
    ).length || 0;

    const healthyItems = totalItems - highRiskItems;

    return (
        <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
                <SummaryCard
                    title="Total Items"
                    value={totalItems}
                    subtitle="In inventory"
                    icon={<TrendingUp fontSize="large" />}
                    color="primary"
                />
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
                <SummaryCard
                    title="Critical Actions"
                    value={criticalItems}
                    subtitle="Need immediate attention"
                    icon={<Warning fontSize="large" />}
                    color="error"
                />
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
                <SummaryCard
                    title="Healthy Items"
                    value={healthyItems}
                    subtitle="In good condition"
                    icon={<CheckCircle fontSize="large" />}
                    color="success"
                />
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
                <SummaryCard
                    title="Potential Loss"
                    value={`$${totalPotentialLoss.toFixed(0)}`}
                    subtitle="At-risk value"
                    icon={<AttachMoney fontSize="large" />}
                    color="warning"
                />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
                <SummaryCard
                    title="Expiring Soon"
                    value={expiringSoon}
                    subtitle="≤ 3 days remaining"
                    icon={<Warning fontSize="large" />}
                    color="error"
                />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
                <SummaryCard
                    title="Average Risk"
                    value={`${averageRisk.toFixed(1)}%`}
                    subtitle="Overall waste risk"
                    icon={<TrendingUp fontSize="large" />}
                    color={averageRisk > 60 ? 'error' : averageRisk > 30 ? 'warning' : 'success'}
                />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
                <SummaryCard
                    title="Expected Waste"
                    value={`$${totalWasteValue.toFixed(0)}`}
                    subtitle="Projected loss value"
                    icon={<AttachMoney fontSize="large" />}
                    color="error"
                />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
                <SummaryCard
                    title="High Risk Items"
                    value={highRiskItems}
                    subtitle="Need monitoring"
                    icon={<Warning fontSize="large" />}
                    color="warning"
                />
            </Grid>
        </Grid>
    );
};
