#!/usr/bin/env python3
"""
CSV Validation Tool for Smart Shelf Life Optimizer
"""
import pandas as pd
import sys
from datetime import datetime

def validate_csv(file_path):
    """Validate CSV file format for the Smart Shelf Life Optimizer."""
    print(f"Validating CSV file: {file_path}")
    print("=" * 50)
    
    try:
        # Read the CSV file
        df = pd.read_csv(file_path)
        print(f"✅ File loaded successfully. Found {len(df)} rows.")
        
        # Check required columns
        required_columns = ['Item_No', 'Lot_No', 'Expiration_Date', 'Quantity', 'Last_Movement_Date']
        print(f"\n📋 Checking required columns: {required_columns}")
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"❌ Missing required columns: {missing_columns}")
            print(f"📝 Found columns: {list(df.columns)}")
            return False
        else:
            print("✅ All required columns present")
        
        # Check for empty values
        print(f"\n🔍 Checking for empty values...")
        for col in required_columns:
            empty_count = df[col].isna().sum()
            if empty_count > 0:
                print(f"⚠️  Column '{col}' has {empty_count} empty values")
            else:
                print(f"✅ Column '{col}' has no empty values")
        
        # Validate date formats
        print(f"\n📅 Validating date formats...")
        date_columns = ['Expiration_Date', 'Last_Movement_Date']
        date_formats = ['%Y-%m-%d', '%m/%d/%Y', '%d/%m/%Y', '%Y/%m/%d']
        
        for col in date_columns:
            print(f"  Checking {col}...")
            valid_dates = 0
            invalid_dates = []
            
            for idx, date_str in enumerate(df[col]):
                if pd.isna(date_str):
                    continue
                    
                date_parsed = False
                for fmt in date_formats:
                    try:
                        datetime.strptime(str(date_str), fmt)
                        date_parsed = True
                        break
                    except ValueError:
                        continue
                
                if date_parsed:
                    valid_dates += 1
                else:
                    invalid_dates.append(f"Row {idx + 2}: '{date_str}'")
            
            if invalid_dates:
                print(f"    ❌ {len(invalid_dates)} invalid dates found:")
                for invalid in invalid_dates[:5]:  # Show first 5
                    print(f"      {invalid}")
                if len(invalid_dates) > 5:
                    print(f"      ... and {len(invalid_dates) - 5} more")
            else:
                print(f"    ✅ All {valid_dates} dates are valid")
        
        # Validate quantity values
        print(f"\n🔢 Validating quantity values...")
        try:
            quantities = pd.to_numeric(df['Quantity'], errors='coerce')
            invalid_quantities = quantities.isna().sum()
            if invalid_quantities > 0:
                print(f"❌ {invalid_quantities} invalid quantity values found")
            else:
                print("✅ All quantity values are valid numbers")
        except Exception as e:
            print(f"❌ Error validating quantities: {e}")
        
        # Show sample data
        print(f"\n📊 Sample data (first 3 rows):")
        print(df.head(3).to_string())
        
        print(f"\n🎉 CSV validation completed!")
        return True
        
    except FileNotFoundError:
        print(f"❌ File not found: {file_path}")
        return False
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

def show_example():
    """Show an example of correct CSV format."""
    print("\n📝 Example of correct CSV format:")
    print("=" * 50)
    example = """Item_No,Lot_No,Expiration_Date,Quantity,Last_Movement_Date
MILK001,LOT20250527,2025-06-10,100,2025-05-27
BREAD001,LOT20250527,2025-05-29,80,2025-05-27
APPLE001,LOT20250527,2025-06-05,200,2025-05-27"""
    print(example)
    print("\n📋 Requirements:")
    print("- Item_No: Unique identifier for the product")
    print("- Lot_No: Lot/batch number")
    print("- Expiration_Date: Date in YYYY-MM-DD, MM/DD/YYYY, DD/MM/YYYY, or YYYY/MM/DD format")
    print("- Quantity: Numeric value")
    print("- Last_Movement_Date: Date in same formats as Expiration_Date")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python validate_csv.py <csv_file_path>")
        print("\nExample: python validate_csv.py my_inventory.csv")
        show_example()
        sys.exit(1)
    
    csv_file = sys.argv[1]
    success = validate_csv(csv_file)
    
    if not success:
        show_example()
        sys.exit(1)
    else:
        print("\n✅ Your CSV file should work with the Smart Shelf Life Optimizer!")
