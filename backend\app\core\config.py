from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    PROJECT_NAME: str = "Smart Shelf Life Optimizer"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"

    # Use SQLite for testing
    SQLALCHEMY_DATABASE_URI: str = "sqlite:///./shelf_life_optimizer.db"

    # PostgreSQL settings (for production)
    POSTGRES_SERVER: str = "db"
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "postgres"
    POSTGRES_DB: str = "shelf_life_optimizer"

    @property
    def get_database_url(self) -> str:
        if os.getenv("USE_POSTGRES", "false").lower() == "true":
            return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}/{self.POSTGRES_DB}"
        return self.SQLALCHEMY_DATABASE_URI

    class Config:
        case_sensitive = True
        env_file = ".env"

settings = Settings()