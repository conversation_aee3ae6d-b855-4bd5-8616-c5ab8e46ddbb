#!/usr/bin/env python3
"""
Simple working Smart Shelf Life Optimizer
"""
from flask import Flask, request, jsonify, render_template_string
import json
from datetime import datetime, timedelta
import io
import csv

app = Flask(__name__)

# In-memory storage for simplicity
inventory_data = []

# HTML template for the application
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Smart Shelf Life Optimizer</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .upload-section { background: #e3f2fd; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
        .analytics-section { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px; }
        .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .metric { text-align: center; margin: 10px 0; }
        .metric-value { font-size: 2em; font-weight: bold; color: #1976d2; }
        .metric-label { color: #666; }
        .risk-high { color: #d32f2f; }
        .risk-medium { color: #f57c00; }
        .risk-low { color: #388e3c; }
        .item-list { max-height: 300px; overflow-y: auto; }
        .item { padding: 10px; border-bottom: 1px solid #eee; }
        .item:last-child { border-bottom: none; }
        button { background: #1976d2; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #1565c0; }
        input[type="file"] { margin: 10px 0; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.success { background: #e8f5e8; color: #2e7d32; }
        .status.error { background: #ffebee; color: #c62828; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍎 Smart Shelf Life Optimizer</h1>
            <p>Monitor inventory freshness and optimize distribution</p>
        </div>

        <div class="upload-section">
            <h3>📤 Import Inventory Data</h3>
            <input type="file" id="csvFile" accept=".csv" />
            <button onclick="uploadCSV()">Upload CSV</button>
            <div id="uploadStatus"></div>
        </div>

        <div class="analytics-section">
            <div class="card">
                <h3>📊 Summary Metrics</h3>
                <div class="metric">
                    <div class="metric-value" id="totalItems">0</div>
                    <div class="metric-label">Total Items</div>
                </div>
                <div class="metric">
                    <div class="metric-value risk-high" id="criticalItems">0</div>
                    <div class="metric-label">Critical Items</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="avgDaysLeft">0</div>
                    <div class="metric-label">Avg Days to Expiry</div>
                </div>
            </div>

            <div class="card">
                <h3>⚠️ High Risk Items</h3>
                <div id="highRiskItems" class="item-list">
                    <div class="item">No data available. Upload a CSV file to see analysis.</div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>📋 All Inventory Items</h3>
            <div id="allItems" class="item-list">
                <div class="item">No data available. Upload a CSV file to see your inventory.</div>
            </div>
        </div>
    </div>

    <script>
        function uploadCSV() {
            const fileInput = document.getElementById('csvFile');
            const file = fileInput.files[0];

            if (!file) {
                showStatus('Please select a CSV file', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            showStatus('Uploading and processing...', 'info');

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showStatus(`✅ Successfully imported ${data.count} items!`, 'success');
                    loadAnalytics();
                } else {
                    showStatus(`❌ Error: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showStatus(`❌ Upload failed: ${error}`, 'error');
            });
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('uploadStatus');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function loadAnalytics() {
            fetch('/analytics')
            .then(response => response.json())
            .then(data => {
                updateMetrics(data.metrics);
                updateHighRiskItems(data.high_risk);
                updateAllItems(data.all_items);
            })
            .catch(error => {
                console.error('Error loading analytics:', error);
            });
        }

        function updateMetrics(metrics) {
            document.getElementById('totalItems').textContent = metrics.total_items;
            document.getElementById('criticalItems').textContent = metrics.critical_items;
            document.getElementById('avgDaysLeft').textContent = metrics.avg_days_left;
        }

        function updateHighRiskItems(items) {
            const container = document.getElementById('highRiskItems');
            if (items.length === 0) {
                container.innerHTML = '<div class="item">✅ No high-risk items found!</div>';
                return;
            }

            container.innerHTML = items.map(item => `
                <div class="item">
                    <strong>${item.name}</strong><br>
                    <span class="risk-${item.risk_level}">${item.days_left} days left</span> •
                    Qty: ${item.quantity} •
                    Risk: ${item.risk_score}%
                </div>
            `).join('');
        }

        function updateAllItems(items) {
            const container = document.getElementById('allItems');
            if (items.length === 0) {
                container.innerHTML = '<div class="item">No data available. Upload a CSV file to see your inventory.</div>';
                return;
            }

            container.innerHTML = items.map(item => `
                <div class="item">
                    <strong>${item.name}</strong><br>
                    ${item.days_left} days left •
                    Qty: ${item.quantity} •
                    <span class="risk-${item.risk_level}">Risk: ${item.risk_score}%</span>
                </div>
            `).join('');
        }

        // Load analytics on page load
        loadAnalytics();
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/upload', methods=['POST'])
def upload_csv():
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file uploaded'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'})

        # Read CSV data
        csv_data = file.read().decode('utf-8')
        csv_reader = csv.DictReader(io.StringIO(csv_data))

        # Clear existing data
        global inventory_data
        inventory_data = []

        # Get column names
        fieldnames = csv_reader.fieldnames or []

        # Process each row
        for index, row in enumerate(csv_reader):
            # Try to find item name/ID column
            item_name = None
            for col in fieldnames:
                if any(keyword in col.lower() for keyword in ['name', 'item', 'product', 'description', 'no']):
                    item_name = str(row[col])
                    break

            if not item_name:
                item_name = f"Item {index + 1}"

            # Try to find quantity column
            quantity = 1
            for col in fieldnames:
                if any(keyword in col.lower() for keyword in ['quantity', 'qty', 'amount', 'stock', 'hand']):
                    try:
                        quantity = int(float(str(row[col]).replace(',', '')))
                        break
                    except:
                        continue

            # Generate expiry date (random between 1-30 days for demo)
            import random
            days_to_expiry = random.randint(1, 30)
            expiry_date = datetime.now() + timedelta(days=days_to_expiry)

            # Calculate risk score
            if days_to_expiry <= 3:
                risk_score = 90
                risk_level = 'high'
            elif days_to_expiry <= 7:
                risk_score = 70
                risk_level = 'medium'
            else:
                risk_score = 30
                risk_level = 'low'

            inventory_data.append({
                'name': item_name,
                'quantity': quantity,
                'days_left': days_to_expiry,
                'expiry_date': expiry_date.isoformat(),
                'risk_score': risk_score,
                'risk_level': risk_level
            })

        return jsonify({
            'success': True,
            'count': len(inventory_data),
            'message': f'Successfully processed {len(inventory_data)} items'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/analytics')
def get_analytics():
    if not inventory_data:
        return jsonify({
            'metrics': {
                'total_items': 0,
                'critical_items': 0,
                'avg_days_left': 0
            },
            'high_risk': [],
            'all_items': []
        })

    # Calculate metrics
    total_items = len(inventory_data)
    critical_items = len([item for item in inventory_data if item['risk_score'] >= 70])
    avg_days_left = round(sum(item['days_left'] for item in inventory_data) / total_items, 1)

    # Get high risk items
    high_risk = [item for item in inventory_data if item['risk_score'] >= 70]
    high_risk.sort(key=lambda x: x['risk_score'], reverse=True)

    # Sort all items by risk
    all_items = sorted(inventory_data, key=lambda x: x['risk_score'], reverse=True)

    return jsonify({
        'metrics': {
            'total_items': total_items,
            'critical_items': critical_items,
            'avg_days_left': avg_days_left
        },
        'high_risk': high_risk[:10],  # Top 10 high risk
        'all_items': all_items[:20]   # Top 20 items
    })

if __name__ == '__main__':
    print("🚀 Starting Simple Smart Shelf Life Optimizer...")
    print("📱 Open http://localhost:5000 in your browser")
    app.run(host='0.0.0.0', port=5000, debug=True)
