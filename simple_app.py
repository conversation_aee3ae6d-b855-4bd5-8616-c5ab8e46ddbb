#!/usr/bin/env python3
"""
Simple working Smart Shelf Life Optimizer
"""
from flask import Flask, request, jsonify, render_template_string
import json
from datetime import datetime, timedelta
import io
import csv
import random
import math

app = Flask(__name__)

# In-memory storage for simplicity
inventory_data = []

# AI Configuration and Mock Data
LOCATIONS = [
    {"name": "Warehouse A", "distance": 5, "demand_factor": 1.2, "capacity": 1000},
    {"name": "Store Downtown", "distance": 15, "demand_factor": 1.8, "capacity": 500},
    {"name": "Store Mall", "distance": 25, "demand_factor": 1.5, "capacity": 300},
    {"name": "Distribution Center", "distance": 50, "demand_factor": 0.8, "capacity": 2000},
    {"name": "Outlet Store", "distance": 35, "demand_factor": 2.0, "capacity": 200}
]

# Mock historical data for AI learning
HISTORICAL_PATTERNS = {
    "food": {"sell_through_rate": 0.85, "seasonal_factor": 1.1},
    "medicine": {"sell_through_rate": 0.95, "seasonal_factor": 1.0},
    "chemical": {"sell_through_rate": 0.70, "seasonal_factor": 0.9},
    "electronics": {"sell_through_rate": 0.80, "seasonal_factor": 1.2},
    "general": {"sell_through_rate": 0.75, "seasonal_factor": 1.0}
}

# HTML template for the application
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Smart Shelf Life Optimizer</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .upload-section { background: #e3f2fd; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
        .analytics-section { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px; }
        .heatmap-section { margin-bottom: 30px; }
        .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .metric { text-align: center; margin: 10px 0; }
        .metric-value { font-size: 2em; font-weight: bold; color: #1976d2; }
        .metric-label { color: #666; }
        .risk-high { color: #d32f2f; }
        .risk-medium { color: #f57c00; }
        .risk-low { color: #388e3c; }
        .item-list { max-height: 300px; overflow-y: auto; }
        .item { padding: 10px; border-bottom: 1px solid #eee; }
        .item:last-child { border-bottom: none; }
        button { background: #1976d2; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #1565c0; }
        input[type="file"] { margin: 10px 0; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .status.success { background: #e8f5e8; color: #2e7d32; }
        .status.error { background: #ffebee; color: #c62828; }
        .heatmap-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; margin-top: 20px; }
        .heatmap-cell { padding: 15px; border-radius: 8px; text-align: center; color: white; font-weight: bold; min-height: 60px; display: flex; flex-direction: column; justify-content: center; }
        .heatmap-cell.critical { background: #d32f2f; }
        .heatmap-cell.high { background: #f57c00; }
        .heatmap-cell.medium { background: #fbc02d; color: #333; }
        .heatmap-cell.low { background: #388e3c; }
        .heatmap-cell-title { font-size: 0.9em; margin-bottom: 5px; }
        .heatmap-cell-value { font-size: 1.2em; }
        .ai-suggestion { padding: 15px; border-left: 4px solid #1976d2; margin: 10px 0; background: #f8f9fa; border-radius: 4px; }
        .ai-suggestion.critical { border-left-color: #d32f2f; background: #ffebee; }
        .ai-suggestion.high { border-left-color: #f57c00; background: #fff3e0; }
        .ai-suggestion.medium { border-left-color: #fbc02d; background: #fffde7; }
        .ai-suggestion-header { font-weight: bold; margin-bottom: 8px; }
        .ai-reasoning { font-size: 0.9em; color: #666; margin-top: 8px; font-style: italic; }
        .ai-score { background: #1976d2; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍎 Smart Shelf Life Optimizer</h1>
            <p>Monitor inventory freshness and optimize distribution</p>
        </div>

        <div class="upload-section">
            <h3>📤 Import Inventory Data</h3>
            <input type="file" id="csvFile" accept=".csv" />
            <button onclick="uploadCSV()">Upload CSV</button>
            <div id="uploadStatus"></div>
        </div>

        <div class="heatmap-section">
            <div class="card">
                <h3>🔥 Shelf Life Heatmap</h3>
                <p>Visual overview of inventory risk levels by category</p>
                <div id="heatmapGrid" class="heatmap-grid">
                    <div class="heatmap-cell medium">
                        <div class="heatmap-cell-title">No Data</div>
                        <div class="heatmap-cell-value">Upload CSV to see heatmap</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>🤖 AI Distribution Suggestions</h3>
            <p>Intelligent recommendations powered by AI analysis</p>
            <div id="aiSuggestions" class="item-list">
                <div class="ai-suggestion medium">
                    <div class="ai-suggestion-header">No AI suggestions available</div>
                    <div>Upload CSV data to see AI-powered distribution recommendations</div>
                </div>
            </div>
        </div>

        <div class="analytics-section">
            <div class="card">
                <h3>📊 Summary Metrics</h3>
                <div class="metric">
                    <div class="metric-value" id="totalItems">0</div>
                    <div class="metric-label">Total Items</div>
                </div>
                <div class="metric">
                    <div class="metric-value risk-high" id="criticalItems">0</div>
                    <div class="metric-label">Critical Items</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="avgDaysLeft">0</div>
                    <div class="metric-label">Avg Days to Expiry</div>
                </div>
            </div>

            <div class="card">
                <h3>⚠️ High Risk Items</h3>
                <div id="highRiskItems" class="item-list">
                    <div class="item">No data available. Upload a CSV file to see analysis.</div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>📋 All Inventory Items</h3>
            <div id="allItems" class="item-list">
                <div class="item">No data available. Upload a CSV file to see your inventory.</div>
            </div>
        </div>
    </div>

    <script>
        function uploadCSV() {
            const fileInput = document.getElementById('csvFile');
            const file = fileInput.files[0];

            if (!file) {
                showStatus('Please select a CSV file', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            showStatus('Uploading and processing...', 'info');

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showStatus(`✅ Successfully imported ${data.count} items!`, 'success');
                    loadAnalytics();
                } else {
                    showStatus(`❌ Error: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showStatus(`❌ Upload failed: ${error}`, 'error');
            });
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('uploadStatus');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function loadAnalytics() {
            fetch('/analytics')
            .then(response => response.json())
            .then(data => {
                updateMetrics(data.metrics);
                updateHeatmap(data.heatmap);
                updateAISuggestions(data.ai_suggestions);
                updateHighRiskItems(data.high_risk);
                updateAllItems(data.all_items);
            })
            .catch(error => {
                console.error('Error loading analytics:', error);
            });
        }

        function updateMetrics(metrics) {
            document.getElementById('totalItems').textContent = metrics.total_items;
            document.getElementById('criticalItems').textContent = metrics.critical_items;
            document.getElementById('avgDaysLeft').textContent = metrics.avg_days_left;
        }

        function updateHeatmap(heatmapData) {
            const container = document.getElementById('heatmapGrid');
            if (!heatmapData || heatmapData.length === 0) {
                container.innerHTML = `
                    <div class="heatmap-cell medium">
                        <div class="heatmap-cell-title">No Data</div>
                        <div class="heatmap-cell-value">Upload CSV to see heatmap</div>
                    </div>
                `;
                return;
            }

            container.innerHTML = heatmapData.map(cell => `
                <div class="heatmap-cell ${cell.risk_class}">
                    <div class="heatmap-cell-title">${cell.category}</div>
                    <div class="heatmap-cell-value">${cell.count} items</div>
                    <div style="font-size: 0.8em;">${cell.avg_days} days avg</div>
                </div>
            `).join('');
        }

        function updateAISuggestions(suggestions) {
            const container = document.getElementById('aiSuggestions');
            if (!suggestions || suggestions.length === 0) {
                container.innerHTML = `
                    <div class="ai-suggestion medium">
                        <div class="ai-suggestion-header">🤖 No AI suggestions available</div>
                        <div>Upload CSV data to see AI-powered distribution recommendations</div>
                    </div>
                `;
                return;
            }

            container.innerHTML = suggestions.map(suggestion => `
                <div class="ai-suggestion ${suggestion.priority.toLowerCase()}">
                    <div class="ai-suggestion-header">
                        🤖 ${suggestion.item_name}
                        <span class="ai-score">AI Score: ${suggestion.ai_urgency_score}</span>
                    </div>
                    <div><strong>Action:</strong> ${suggestion.ai_action}</div>
                    <div><strong>Recommended Location:</strong> ${suggestion.recommended_location} (${suggestion.estimated_transit_days} days transit)</div>
                    <div><strong>Quantity:</strong> ${suggestion.quantity} • <strong>Days Left:</strong> ${suggestion.days_left}</div>
                    <div class="ai-reasoning">${suggestion.reasoning}</div>
                </div>
            `).join('');
        }

        function updateHighRiskItems(items) {
            const container = document.getElementById('highRiskItems');
            if (items.length === 0) {
                container.innerHTML = '<div class="item">✅ No high-risk items found!</div>';
                return;
            }

            container.innerHTML = items.map(item => `
                <div class="item">
                    <strong>${item.name}</strong><br>
                    <span class="risk-${item.risk_level}">${item.days_left} days left</span> •
                    Qty: ${item.quantity} •
                    Risk: ${item.risk_score}%
                </div>
            `).join('');
        }

        function updateAllItems(items) {
            const container = document.getElementById('allItems');
            if (items.length === 0) {
                container.innerHTML = '<div class="item">No data available. Upload a CSV file to see your inventory.</div>';
                return;
            }

            container.innerHTML = items.map(item => `
                <div class="item">
                    <strong>${item.name}</strong><br>
                    ${item.days_left} days left •
                    Qty: ${item.quantity} •
                    <span class="risk-${item.risk_level}">Risk: ${item.risk_score}%</span>
                </div>
            `).join('');
        }

        // Load analytics on page load
        loadAnalytics();
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/upload', methods=['POST'])
def upload_csv():
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file uploaded'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'})

        # Read CSV data with multiple encoding attempts
        raw_data = file.read()
        csv_data = None

        # Try different encodings
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1', 'utf-16']

        for encoding in encodings:
            try:
                csv_data = raw_data.decode(encoding)
                print(f"✅ Successfully decoded with {encoding}")
                break
            except UnicodeDecodeError:
                continue

        if csv_data is None:
            return jsonify({'success': False, 'error': 'Could not decode file. Please save your CSV as UTF-8 format.'})

        try:
            csv_reader = csv.DictReader(io.StringIO(csv_data))

            # Clear existing data
            global inventory_data
            inventory_data = []

            # Get column names
            fieldnames = csv_reader.fieldnames or []

            if not fieldnames:
                return jsonify({'success': False, 'error': 'CSV file appears to be empty or malformed'})

            print(f"📋 Found CSV columns: {fieldnames}")

        except Exception as e:
            return jsonify({'success': False, 'error': f'Error reading CSV: {str(e)}'})

        # Process each row
        for index, row in enumerate(csv_reader):
            # Try to find item name/ID column
            item_name = None
            for col in fieldnames:
                if any(keyword in col.lower() for keyword in ['name', 'item', 'product', 'description', 'no']):
                    item_name = str(row[col])
                    break

            if not item_name:
                item_name = f"Item {index + 1}"

            # Try to find quantity column
            quantity = 1
            for col in fieldnames:
                if any(keyword in col.lower() for keyword in ['quantity', 'qty', 'amount', 'stock', 'hand']):
                    try:
                        quantity = int(float(str(row[col]).replace(',', '')))
                        break
                    except:
                        continue

            # Generate expiry date (random between 1-30 days for demo)
            import random
            days_to_expiry = random.randint(1, 30)
            expiry_date = datetime.now() + timedelta(days=days_to_expiry)

            # Calculate risk score
            if days_to_expiry <= 3:
                risk_score = 90
                risk_level = 'high'
            elif days_to_expiry <= 7:
                risk_score = 70
                risk_level = 'medium'
            else:
                risk_score = 30
                risk_level = 'low'

            inventory_data.append({
                'name': item_name,
                'quantity': quantity,
                'days_left': days_to_expiry,
                'expiry_date': expiry_date.isoformat(),
                'risk_score': risk_score,
                'risk_level': risk_level
            })

        return jsonify({
            'success': True,
            'count': len(inventory_data),
            'message': f'Successfully processed {len(inventory_data)} items'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def calculate_ai_urgency_score(item):
    """AI-powered urgency scoring based on multiple factors."""
    # Base urgency from days remaining
    days_left = item['days_left']
    if days_left <= 0:
        urgency_base = 100
    elif days_left <= 3:
        urgency_base = 90
    elif days_left <= 7:
        urgency_base = 70
    elif days_left <= 14:
        urgency_base = 50
    else:
        urgency_base = 20

    # Category-specific adjustments
    category = get_item_category(item['name'])
    pattern = HISTORICAL_PATTERNS.get(category, HISTORICAL_PATTERNS['general'])

    # Adjust based on sell-through rate
    sell_through_adjustment = (1 - pattern['sell_through_rate']) * 20

    # Seasonal factor
    seasonal_adjustment = (pattern['seasonal_factor'] - 1) * 10

    # Quantity factor (more quantity = higher urgency)
    quantity_factor = min(item['quantity'] / 100, 1) * 10

    # Final AI score
    ai_score = urgency_base + sell_through_adjustment + seasonal_adjustment + quantity_factor
    return min(max(ai_score, 0), 100)

def get_item_category(item_name):
    """Determine item category for AI processing."""
    name_lower = item_name.lower()
    if any(word in name_lower for word in ['food', 'meat', 'dairy', 'milk', 'cheese', 'bread']):
        return "food"
    elif any(word in name_lower for word in ['medicine', 'drug', 'pill', 'tablet', 'vaccine']):
        return "medicine"
    elif any(word in name_lower for word in ['chemical', 'acid', 'solution', 'reagent']):
        return "chemical"
    elif any(word in name_lower for word in ['electronic', 'battery', 'device', 'component']):
        return "electronics"
    return "general"

def ai_distribution_suggestions(inventory_data):
    """AI-powered distribution suggestions based on multiple factors."""
    suggestions = []

    for item in inventory_data:
        if item['risk_score'] >= 50:  # Only suggest for medium+ risk items
            # Calculate AI urgency score
            ai_urgency = calculate_ai_urgency_score(item)

            # Find best location using AI logic
            best_location = find_optimal_location(item)

            # Generate AI recommendation
            action = generate_ai_action(item, ai_urgency)

            suggestions.append({
                'item_name': item['name'],
                'quantity': item['quantity'],
                'days_left': item['days_left'],
                'ai_urgency_score': round(ai_urgency, 1),
                'recommended_location': best_location['name'],
                'estimated_transit_days': calculate_transit_time(best_location['distance']),
                'ai_action': action,
                'reasoning': generate_ai_reasoning(item, best_location, ai_urgency),
                'priority': get_priority_level(ai_urgency)
            })

    # Sort by AI urgency score
    suggestions.sort(key=lambda x: x['ai_urgency_score'], reverse=True)
    return suggestions[:15]  # Top 15 AI recommendations

def find_optimal_location(item):
    """AI logic to find the best location for an item."""
    category = get_item_category(item['name'])
    pattern = HISTORICAL_PATTERNS[category]

    best_location = None
    best_score = -1

    for location in LOCATIONS:
        # Calculate location score based on AI factors
        demand_score = location['demand_factor'] * pattern['sell_through_rate'] * 30
        distance_penalty = location['distance'] * 0.5
        capacity_bonus = min(location['capacity'] / 1000, 1) * 10

        # Time urgency factor
        transit_time = calculate_transit_time(location['distance'])
        urgency_factor = max(0, (item['days_left'] - transit_time)) * 2

        total_score = demand_score - distance_penalty + capacity_bonus + urgency_factor

        if total_score > best_score:
            best_score = total_score
            best_location = location

    return best_location or LOCATIONS[0]

def calculate_transit_time(distance):
    """Calculate estimated transit time based on distance."""
    if distance <= 10:
        return 1
    elif distance <= 30:
        return 2
    elif distance <= 60:
        return 3
    else:
        return 4

def generate_ai_action(item, urgency_score):
    """Generate AI-powered action recommendations."""
    if urgency_score >= 90:
        return "URGENT: Ship immediately or apply deep discount"
    elif urgency_score >= 70:
        return "HIGH: Prioritize for next shipment"
    elif urgency_score >= 50:
        return "MEDIUM: Include in regular distribution"
    else:
        return "LOW: Monitor and plan for future shipment"

def generate_ai_reasoning(item, location, urgency_score):
    """Generate AI explanation for the recommendation."""
    category = get_item_category(item['name'])
    transit_time = calculate_transit_time(location['distance'])

    reasoning = f"AI Analysis: {category.title()} category with {item['days_left']} days remaining. "
    reasoning += f"Recommended {location['name']} due to {location['demand_factor']:.1f}x demand factor. "
    reasoning += f"Transit time: {transit_time} days. "

    if urgency_score >= 80:
        reasoning += "CRITICAL: Immediate action required to prevent waste."
    elif urgency_score >= 60:
        reasoning += "HIGH PRIORITY: Should be processed within 24 hours."
    else:
        reasoning += "Standard processing timeline acceptable."

    return reasoning

def get_priority_level(urgency_score):
    """Convert urgency score to priority level."""
    if urgency_score >= 80:
        return "CRITICAL"
    elif urgency_score >= 60:
        return "HIGH"
    elif urgency_score >= 40:
        return "MEDIUM"
    else:
        return "LOW"

def generate_heatmap_data(inventory_data):
    """Generate heatmap data by categorizing items by risk level and type."""
    if not inventory_data:
        return []

    # Categorize items by risk and type
    categories = {}

    for item in inventory_data:
        # Determine category based on item name patterns
        category = "General"
        name_lower = item['name'].lower()

        if any(word in name_lower for word in ['food', 'meat', 'dairy', 'milk', 'cheese', 'bread']):
            category = "Food"
        elif any(word in name_lower for word in ['medicine', 'drug', 'pill', 'tablet', 'vaccine']):
            category = "Medicine"
        elif any(word in name_lower for word in ['chemical', 'acid', 'solution', 'reagent']):
            category = "Chemical"
        elif any(word in name_lower for word in ['electronic', 'battery', 'device', 'component']):
            category = "Electronics"

        # Determine risk level
        if item['days_left'] <= 3:
            risk_level = "Critical"
            risk_class = "critical"
        elif item['days_left'] <= 7:
            risk_level = "High"
            risk_class = "high"
        elif item['days_left'] <= 14:
            risk_level = "Medium"
            risk_class = "medium"
        else:
            risk_level = "Low"
            risk_class = "low"

        # Create category key
        key = f"{category} - {risk_level}"

        if key not in categories:
            categories[key] = {
                'category': category,
                'risk_level': risk_level,
                'risk_class': risk_class,
                'items': [],
                'total_days': 0
            }

        categories[key]['items'].append(item)
        categories[key]['total_days'] += item['days_left']

    # Convert to heatmap format
    heatmap_data = []
    for key, data in categories.items():
        count = len(data['items'])
        avg_days = round(data['total_days'] / count, 1) if count > 0 else 0

        heatmap_data.append({
            'category': f"{data['category']} ({data['risk_level']})",
            'count': count,
            'avg_days': avg_days,
            'risk_class': data['risk_class']
        })

    # Sort by risk level (critical first)
    risk_order = {'critical': 0, 'high': 1, 'medium': 2, 'low': 3}
    heatmap_data.sort(key=lambda x: risk_order.get(x['risk_class'], 4))

    return heatmap_data

@app.route('/analytics')
def get_analytics():
    if not inventory_data:
        return jsonify({
            'metrics': {
                'total_items': 0,
                'critical_items': 0,
                'avg_days_left': 0
            },
            'high_risk': [],
            'all_items': []
        })

    # Calculate metrics
    total_items = len(inventory_data)
    critical_items = len([item for item in inventory_data if item['risk_score'] >= 70])
    avg_days_left = round(sum(item['days_left'] for item in inventory_data) / total_items, 1)

    # Get high risk items
    high_risk = [item for item in inventory_data if item['risk_score'] >= 70]
    high_risk.sort(key=lambda x: x['risk_score'], reverse=True)

    # Sort all items by risk
    all_items = sorted(inventory_data, key=lambda x: x['risk_score'], reverse=True)

    # Generate heatmap data
    heatmap_data = generate_heatmap_data(inventory_data)

    # Generate AI distribution suggestions
    ai_suggestions = ai_distribution_suggestions(inventory_data)

    return jsonify({
        'metrics': {
            'total_items': total_items,
            'critical_items': critical_items,
            'avg_days_left': avg_days_left
        },
        'heatmap': heatmap_data,
        'ai_suggestions': ai_suggestions,
        'high_risk': high_risk[:10],  # Top 10 high risk
        'all_items': all_items[:20]   # Top 20 items
    })

if __name__ == '__main__':
    print("🚀 Starting Simple Smart Shelf Life Optimizer...")
    print("📱 Open http://localhost:5000 in your browser")
    app.run(host='0.0.0.0', port=5000, debug=True)
