from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from app.db.session import get_db
from app.services.import_service import import_inventory_data
import pandas as pd
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/import-csv")
async def import_csv(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Import inventory data from CSV file."""
    if not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="File must be a CSV")

    try:
        # Read file contents
        contents = await file.read()
        if not contents:
            raise HTTPException(status_code=400, detail="File is empty")

        # Decode contents
        try:
            csv_data = contents.decode('utf-8')
        except UnicodeDecodeError:
            try:
                csv_data = contents.decode('latin-1')
            except UnicodeDecodeError:
                raise HTTPException(status_code=400, detail="File encoding not supported. Please use UTF-8 or Latin-1")

        # Import data
        try:
            result = import_inventory_data(db, csv_data)
            if not result["success"]:
                raise HTTPException(status_code=400, detail=result["error"])
            return result
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Error importing data: {str(e)}")
            logger.error(f"Error details: {error_details}")
            raise HTTPException(status_code=500, detail=f"Error importing data: {str(e)}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")
    finally:
        await file.close()