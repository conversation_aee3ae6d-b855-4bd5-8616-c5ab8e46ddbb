from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from app.db.session import get_db
from app.services.import_service import import_inventory_data
import pandas as pd
import logging
import json

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/import-csv")
async def import_csv(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Import inventory data from CSV file."""
    if not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="File must be a CSV")

    try:
        # Read file contents
        contents = await file.read()
        if not contents:
            raise HTTPException(status_code=400, detail="File is empty")

        # Decode contents
        try:
            csv_data = contents.decode('utf-8')
        except UnicodeDecodeError:
            try:
                csv_data = contents.decode('latin-1')
            except UnicodeDecodeError:
                raise HTTPException(status_code=400, detail="File encoding not supported. Please use UTF-8 or Latin-1")

        # Try to auto-detect ERP format and apply mapping
        from io import StringIO
        import pandas as pd

        try:
            df = pd.read_csv(StringIO(csv_data))

            # Check if this looks like your ERP format
            erp_columns = df.columns.tolist()

            # Auto-detect common ERP mappings
            auto_mapping = {}
            for col in erp_columns:
                col_lower = col.lower()
                if col.lower() == 'no.' or 'item' in col_lower or 'product' in col_lower:
                    auto_mapping[col] = 'Item_No'
                elif 'quantity on hand' in col_lower or 'qty on hand' in col_lower:
                    auto_mapping[col] = 'Quantity'
                elif 'case units on hand' in col_lower and 'Quantity' not in auto_mapping.values():
                    auto_mapping[col] = 'Quantity'

            # If we detected ERP format, use mapping
            if auto_mapping and 'Item_No' in auto_mapping.values() and 'Quantity' in auto_mapping.values():
                print(f"Auto-detected ERP format, applying mapping: {auto_mapping}")
                result = import_inventory_data(db, csv_data, auto_mapping)
            else:
                # Try standard format
                result = import_inventory_data(db, csv_data)

        except Exception as e:
            # Fallback to standard import
            result = import_inventory_data(db, csv_data)

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")
    finally:
        await file.close()


@router.post("/analyze-csv")
async def analyze_csv_format(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Analyze CSV file format and suggest column mappings."""
    try:
        # Read file content
        contents = await file.read()
        try:
            csv_data = contents.decode('utf-8')
        except UnicodeDecodeError:
            try:
                csv_data = contents.decode('latin-1')
            except UnicodeDecodeError:
                raise HTTPException(status_code=400, detail="File encoding not supported. Please use UTF-8 or Latin-1")

        # Analyze the CSV structure
        from io import StringIO
        import pandas as pd

        df = pd.read_csv(StringIO(csv_data))

        # Get column information
        columns_info = []
        for col in df.columns:
            sample_values = df[col].dropna().head(3).tolist()
            columns_info.append({
                "name": col,
                "sample_values": sample_values,
                "data_type": str(df[col].dtype),
                "non_null_count": int(df[col].count()),
                "null_count": int(df[col].isna().sum())
            })

        # Suggest mappings based on common patterns
        suggested_mappings = {}
        item_patterns = ['item', 'product', 'sku', 'code', 'part', 'no.', 'number']
        lot_patterns = ['lot', 'batch', 'serial']
        expiry_patterns = ['expir', 'exp', 'due', 'best', 'use']
        qty_patterns = ['qty', 'quantity', 'stock', 'amount', 'count', 'hand', 'units']
        date_patterns = ['date', 'time', 'updated', 'modified', 'created']

        for col in df.columns:
            col_lower = col.lower()

            if any(pattern in col_lower for pattern in item_patterns):
                suggested_mappings[col] = "Item_No"
            elif any(pattern in col_lower for pattern in lot_patterns):
                suggested_mappings[col] = "Lot_No"
            elif any(pattern in col_lower for pattern in expiry_patterns):
                suggested_mappings[col] = "Expiration_Date"
            elif any(pattern in col_lower for pattern in qty_patterns):
                suggested_mappings[col] = "Quantity"
            elif any(pattern in col_lower for pattern in date_patterns) and 'expir' not in col_lower:
                suggested_mappings[col] = "Last_Movement_Date"

        return {
            "success": True,
            "total_rows": len(df),
            "columns": columns_info,
            "suggested_mappings": suggested_mappings,
            "required_fields": ["Item_No", "Lot_No", "Expiration_Date", "Quantity", "Last_Movement_Date"],
            "sample_data": df.head(3).to_dict('records')
        }

    except Exception as e:
        logger.error(f"Error analyzing CSV: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error analyzing CSV: {str(e)}")
    finally:
        await file.close()


@router.post("/import-csv-with-mapping")
async def import_csv_with_mapping(
    file: UploadFile = File(...),
    column_mapping: str = Form(...),
    db: Session = Depends(get_db)
):
    """Import CSV with custom column mapping for ERP systems."""
    try:
        # Parse column mapping
        try:
            mapping_dict = json.loads(column_mapping)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid column mapping JSON")

        # Read file content
        contents = await file.read()
        try:
            csv_data = contents.decode('utf-8')
        except UnicodeDecodeError:
            try:
                csv_data = contents.decode('latin-1')
            except UnicodeDecodeError:
                raise HTTPException(status_code=400, detail="File encoding not supported. Please use UTF-8 or Latin-1")

        # Import data with mapping
        result = import_inventory_data(db, csv_data, mapping_dict)
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")
    finally:
        await file.close()