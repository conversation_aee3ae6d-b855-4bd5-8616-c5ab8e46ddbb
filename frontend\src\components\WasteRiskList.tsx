import React from 'react';
import {
    List,
    ListItem,
    ListItemText,
    Typography,
    Box,
    Chip,
    CircularProgress,
    LinearProgress,
} from '@mui/material';
import { useQuery } from 'react-query';
import { getWasteRiskAnalysis } from '../services/api';
import { WasteRisk } from '../types';

export const WasteRiskList: React.FC = () => {
    const { data, isLoading, error } = useQuery<WasteRisk[]>('wasteRisk', getWasteRiskAnalysis);

    if (isLoading) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return <div>Error loading waste risk analysis</div>;
    }

    if (!data) {
        return <div>No data available</div>;
    }

    const getChipColor = (status: string) => {
        switch (status) {
            case 'High Risk':
                return 'error';
            case 'Medium Risk':
                return 'warning';
            case 'Low Risk':
                return 'success';
            default:
                return 'default';
        }
    };

    const getProgressColor = (risk: number) => {
        if (risk >= 70) return 'error';
        if (risk >= 40) return 'warning';
        return 'success';
    };

    return (
        <List sx={{ width: '100%', bgcolor: 'background.paper', overflow: 'auto', maxHeight: 300 }}>
            {data.map((item) => (
                <ListItem
                    key={item.id}
                    alignItems="flex-start"
                    sx={{
                        borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                        '&:last-child': {
                            borderBottom: 'none',
                        },
                    }}
                >
                    <ListItemText
                        primary={
                            <Box display="flex" alignItems="center" gap={1}>
                                <Typography variant="subtitle1">{item.product_name}</Typography>
                                <Chip
                                    label={item.status}
                                    color={getChipColor(item.status)}
                                    size="small"
                                />
                            </Box>
                        }
                        secondary={
                            <React.Fragment>
                                <Typography
                                    component="span"
                                    variant="body2"
                                    color="text.primary"
                                    display="block"
                                >
                                    Location: {item.location}
                                </Typography>
                                <Typography
                                    component="span"
                                    variant="body2"
                                    color="text.primary"
                                    display="block"
                                >
                                    Quantity: {item.quantity}
                                </Typography>
                                <Typography
                                    component="span"
                                    variant="body2"
                                    color="text.primary"
                                    display="block"
                                >
                                    Days to Expiry: {item.days_to_expiry}
                                </Typography>
                                <Typography
                                    component="span"
                                    variant="body2"
                                    color="text.primary"
                                    display="block"
                                >
                                    Daily Consumption: {item.daily_consumption.toFixed(2)}
                                </Typography>
                                <Box sx={{ mt: 1 }}>
                                    <Typography variant="body2" color="text.secondary">
                                        Waste Risk: {item.waste_risk.toFixed(1)}%
                                    </Typography>
                                    <LinearProgress
                                        variant="determinate"
                                        value={item.waste_risk}
                                        color={getProgressColor(item.waste_risk)}
                                        sx={{ mt: 0.5 }}
                                    />
                                </Box>
                            </React.Fragment>
                        }
                    />
                </ListItem>
            ))}
        </List>
    );
}; 