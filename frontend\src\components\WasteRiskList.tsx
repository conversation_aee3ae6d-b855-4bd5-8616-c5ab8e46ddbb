import React from 'react';
import {
    List,
    ListItem,
    ListItemText,
    Typography,
    Box,
    Chip,
    CircularProgress,
    LinearProgress,
} from '@mui/material';
import { useQuery } from 'react-query';
import { getWasteRiskAnalysis } from '../services/api';
import { WasteRisk } from '../types';

export const WasteRiskList: React.FC = () => {
    const { data, isLoading, error } = useQuery<WasteRisk[]>('wasteRisk', getWasteRiskAnalysis);

    if (isLoading) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return <div>Error loading waste risk analysis</div>;
    }

    if (!data || data.length === 0) {
        return (
            <Box sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                    📊 No waste risk data available
                </Typography>
                <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 1 }}>
                    Import inventory data to see waste risk analysis
                </Typography>
            </Box>
        );
    }

    const getChipColor = (status: string) => {
        switch (status) {
            case 'Critical Risk':
                return 'error';
            case 'High Risk':
                return 'error';
            case 'Medium Risk':
                return 'warning';
            case 'Low Risk':
                return 'info';
            case 'Minimal Risk':
                return 'success';
            default:
                return 'default';
        }
    };

    const getProgressColor = (risk: number) => {
        if (risk >= 70) return 'error';
        if (risk >= 40) return 'warning';
        return 'success';
    };

    return (
        <List sx={{ width: '100%', bgcolor: 'background.paper', overflow: 'auto', maxHeight: 300 }}>
            {data.map((item) => (
                <ListItem
                    key={item.id}
                    alignItems="flex-start"
                    sx={{
                        borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                        '&:last-child': {
                            borderBottom: 'none',
                        },
                    }}
                >
                    <ListItemText
                        primary={
                            <Box display="flex" alignItems="center" gap={1}>
                                <Typography variant="subtitle1">{item.product_name}</Typography>
                                <Chip
                                    label={item.status}
                                    color={getChipColor(item.status)}
                                    size="small"
                                />
                            </Box>
                        }
                        secondary={
                            <React.Fragment>
                                <Typography
                                    component="span"
                                    variant="body2"
                                    color="text.primary"
                                    display="block"
                                >
                                    📍 {item.location} • 📦 {item.quantity} units • ⏰ {item.days_to_expiry} days • 🌡️ {item.current_temp}°C
                                </Typography>
                                <Typography
                                    component="span"
                                    variant="body2"
                                    color={item.status === 'Critical Risk' ? 'error.main' : 'text.primary'}
                                    display="block"
                                    sx={{ fontWeight: 'medium', mt: 0.5 }}
                                >
                                    {item.recommendation || 'Monitor condition'}
                                </Typography>
                                {item.expected_waste_value && item.expected_waste_value > 0 && (
                                    <Typography
                                        component="span"
                                        variant="caption"
                                        color="error.main"
                                        display="block"
                                    >
                                        💰 Expected waste value: ${item.expected_waste_value} ({item.waste_probability}% probability)
                                    </Typography>
                                )}
                                {item.action_deadline && (
                                    <Typography
                                        component="span"
                                        variant="caption"
                                        color="warning.main"
                                        display="block"
                                    >
                                        ⏰ Action needed: {item.action_deadline}
                                    </Typography>
                                )}
                                <Box sx={{ mt: 1 }}>
                                    <Typography variant="body2" color="text.secondary">
                                        Waste Risk: {item.waste_risk?.toFixed(1) || 0}%
                                    </Typography>
                                    <LinearProgress
                                        variant="determinate"
                                        value={item.waste_risk || 0}
                                        color={getProgressColor(item.waste_risk || 0)}
                                        sx={{ mt: 0.5 }}
                                    />
                                </Box>
                            </React.Fragment>
                        }
                    />
                </ListItem>
            ))}
        </List>
    );
};