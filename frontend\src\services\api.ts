import axios from 'axios';
import { HeatmapItem, DistributionSuggestion, WasteRisk, ImportResult } from '../types';

const API_BASE_URL = 'http://localhost:8000/api/v1';

const api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
    },
    withCredentials: true,
});

// Add response interceptor for better error handling
api.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            console.error('API Error:', error.response.data);
            const errorMessage = error.response.data.detail || 'API Error';
            return Promise.reject(new Error(errorMessage));
        } else if (error.request) {
            // The request was made but no response was received
            console.error('Network Error:', error.request);
            return Promise.reject(new Error('Network Error - Please check if the backend service is running'));
        } else {
            // Something happened in setting up the request that triggered an Error
            console.error('Error:', error.message);
            return Promise.reject(error);
        }
    }
);

export const getHeatmapData = async (): Promise<HeatmapItem[]> => {
    const response = await api.get('/analytics/heatmap');
    return response.data;
};

export const getDistributionSuggestions = async (): Promise<DistributionSuggestion[]> => {
    const response = await api.get('/analytics/distribution-suggestions');
    return response.data;
};

export const getWasteRiskAnalysis = async (): Promise<WasteRisk[]> => {
    const response = await api.get('/analytics/waste-risk');
    return response.data;
};

export const importInventoryData = async (file: File): Promise<ImportResult> => {
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        const response = await api.post('/inventory/import-csv', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        return response.data;
    } catch (error) {
        if (error instanceof Error) {
            throw new Error(`Error uploading file: ${error.message}`);
        }
        throw error;
    }
};

export const generateSampleData = async () => {
    const response = await api.get('/analytics/generate-sample-data');
    return response.data;
}; 