from fastapi import APIRouter, HTTPException
from app.services.data_generator import (
    generate_fun_data,
    generate_distribution_suggestions,
    generate_waste_risk_analysis
)

router = APIRouter()

@router.get("/generate-sample-data")
async def generate_sample_data():
    """Generate fun sample data for visualization."""
    try:
        # Generate base inventory data
        items = generate_fun_data()
        
        # Generate derived data
        distribution_suggestions = generate_distribution_suggestions(items)
        waste_risk_analysis = generate_waste_risk_analysis(items)
        
        return {
            "heatmap": items,
            "distribution_suggestions": distribution_suggestions,
            "waste_risk_analysis": waste_risk_analysis
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 