from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Enum
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from .base import Base

class ProductCategory(enum.Enum):
    DAIRY = "dairy"
    MEAT = "meat"
    PRODUCE = "produce"
    BAKERY = "bakery"
    FROZEN = "frozen"
    OTHER = "other"

class Product(Base):
    __tablename__ = "products"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    sku = Column(String, unique=True, index=True)
    category = Column(Enum(ProductCategory))
    default_shelf_life_days = Column(Integer)
    min_shelf_life_days = Column(Integer)
    optimal_storage_temp = Column(Float)

    lots = relationship("Lot", back_populates="product")
    inventory_items = relationship("InventoryItem", back_populates="product")

class Lot(Base):
    __tablename__ = "lots"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"))
    lot_number = Column(String, unique=True, index=True)
    production_date = Column(DateTime)
    expiration_date = Column(DateTime)
    quantity = Column(Integer)

    product = relationship("Product", back_populates="lots")
    inventory_items = relationship("InventoryItem", back_populates="lot")

class InventoryItem(Base):
    __tablename__ = "inventory_items"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"))
    lot_id = Column(Integer, ForeignKey("lots.id"))
    location = Column(String)
    quantity = Column(Integer)
    current_temp = Column(Float)
    status = Column(String)  # normal, at_risk, expired
    last_updated = Column(DateTime, default=datetime.utcnow)

    product = relationship("Product", back_populates="inventory_items")
    lot = relationship("Lot", back_populates="inventory_items") 