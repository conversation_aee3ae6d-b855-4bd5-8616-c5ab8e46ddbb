#!/usr/bin/env python3
"""
Simple test to check if the application is working
"""
import requests
import time

def test_application():
    print("🧪 Testing Smart Shelf Life Optimizer Application...")
    
    # Test frontend
    try:
        response = requests.get("http://localhost", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is accessible")
        else:
            print(f"❌ Frontend returned status {response.status_code}")
    except Exception as e:
        print(f"❌ Frontend not accessible: {e}")
    
    # Test backend API
    try:
        response = requests.get("http://localhost:8000/api/v1/analytics/heatmap", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend API working. Heatmap data points: {len(data)}")
        else:
            print(f"❌ Backend API returned status {response.status_code}")
    except Exception as e:
        print(f"❌ Backend API not accessible: {e}")
    
    # Test CSV upload endpoint
    try:
        # Create a simple test CSV
        test_csv = """Item,Quantity,Description
ITEM001,10,Test Product 1
ITEM002,5,Test Product 2
ITEM003,15,Test Product 3"""
        
        files = {'file': ('test.csv', test_csv, 'text/csv')}
        response = requests.post("http://localhost:8000/api/v1/inventory/import", files=files, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ CSV Import working: {result.get('message', 'Success')}")
        else:
            print(f"❌ CSV Import failed with status {response.status_code}")
            try:
                error = response.json()
                print(f"   Error: {error}")
            except:
                print(f"   Raw response: {response.text}")
    except Exception as e:
        print(f"❌ CSV Import test failed: {e}")

if __name__ == "__main__":
    test_application()
