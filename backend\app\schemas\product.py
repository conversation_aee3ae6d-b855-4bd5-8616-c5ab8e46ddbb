from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from app.models.product import ProductCategory

class ProductBase(BaseModel):
    name: str
    sku: str
    category: ProductCategory
    default_shelf_life_days: int = Field(gt=0)
    min_shelf_life_days: int = Field(gt=0)
    optimal_storage_temp: float

class ProductCreate(ProductBase):
    pass

class ProductUpdate(BaseModel):
    name: Optional[str] = None
    sku: Optional[str] = None
    category: Optional[ProductCategory] = None
    default_shelf_life_days: Optional[int] = Field(None, gt=0)
    min_shelf_life_days: Optional[int] = Field(None, gt=0)
    optimal_storage_temp: Optional[float] = None

class ProductResponse(ProductBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True 