from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from app.db.session import get_db
from app.services.analytics_service import (
    calculate_shelf_life_heatmap,
    get_distribution_suggestions,
    analyze_waste_risk
)

router = APIRouter()

@router.get("/heatmap", response_model=List[Dict[str, Any]])
def get_shelf_life_heatmap(db: Session = Depends(get_db)):
    """Get shelf life heatmap data for all inventory items."""
    return calculate_shelf_life_heatmap(db)

@router.get("/distribution-suggestions", response_model=List[Dict[str, Any]])
def get_distribution_recommendations(db: Session = Depends(get_db)):
    """Get distribution suggestions based on expiry dates and inventory status."""
    return get_distribution_suggestions(db)

@router.get("/waste-risk", response_model=List[Dict[str, Any]])
def get_waste_risk_analysis(db: Session = Depends(get_db)):
    """Get waste risk analysis for all inventory items."""
    return analyze_waste_risk(db) 