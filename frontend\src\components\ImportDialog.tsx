import React, { useState } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>alogT<PERSON>le,
    <PERSON>alogContent,
    Dialog<PERSON>ctions,
    <PERSON>ton,
    Box,
    Typography,
    Alert,
    CircularProgress,
    Stepper,
    Step,
    StepLabel,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Chip,
    Grid,
} from '@mui/material';
import { useMutation, useQueryClient } from 'react-query';
import { importInventoryData, analyzeCSV, importWithMapping } from '../services/api';

interface ImportDialogProps {
    open: boolean;
    onClose: () => void;
}

interface ColumnMapping {
    [erpColumn: string]: string;
}

interface CSVAnalysis {
    success: boolean;
    total_rows: number;
    columns: Array<{
        name: string;
        sample_values: any[];
        data_type: string;
        non_null_count: number;
        null_count: number;
    }>;
    suggested_mappings: ColumnMapping;
    required_fields: string[];
    sample_data: any[];
}

export const ImportDialog: React.FC<ImportDialogProps> = ({ open, onClose }) => {
    const [activeStep, setActiveStep] = useState(0);
    const [file, setFile] = useState<File | null>(null);
    const [analysis, setAnalysis] = useState<CSVAnalysis | null>(null);
    const [columnMapping, setColumnMapping] = useState<ColumnMapping>({});
    const [useAutoMapping, setUseAutoMapping] = useState(true);
    const queryClient = useQueryClient();

    const steps = ['Upload File', 'Review Mapping', 'Import Data'];

    const analyzeMutation = useMutation(analyzeCSV, {
        onSuccess: (data: CSVAnalysis) => {
            setAnalysis(data);
            setColumnMapping(data.suggested_mappings);
            setActiveStep(1);
        },
    });

    const importMutation = useMutation(
        ({ file, mapping }: { file: File; mapping: ColumnMapping }) =>
            importWithMapping(file, mapping),
        {
            onSuccess: () => {
                queryClient.invalidateQueries(['heatmap', 'distribution', 'wasteRisk']);
                handleClose();
            },
        }
    );

    const legacyMutation = useMutation(importInventoryData, {
        onSuccess: (data) => {
            // Force refresh all data
            queryClient.invalidateQueries(['heatmap', 'distribution', 'wasteRisk']);
            queryClient.refetchQueries(['heatmap', 'distribution', 'wasteRisk']);

            // Show success message with details
            console.log('Import successful:', data);

            // Close dialog after a brief delay to show success message
            setTimeout(() => {
                handleClose();
                // Force a page refresh to ensure data is visible
                window.location.reload();
            }, 2000);
        },
    });

    const handleClose = () => {
        setActiveStep(0);
        setFile(null);
        setAnalysis(null);
        setColumnMapping({});
        setUseAutoMapping(true);
        onClose();
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            setFile(event.target.files[0]);
        }
    };

    const handleAnalyze = () => {
        if (file) {
            analyzeMutation.mutate(file);
        }
    };

    const handleTryLegacyImport = () => {
        if (file) {
            legacyMutation.mutate(file);
        }
    };

    const handleImportWithMapping = () => {
        if (file && columnMapping) {
            importMutation.mutate({ file, mapping: columnMapping });
        }
    };

    const handleMappingChange = (erpColumn: string, targetColumn: string) => {
        setColumnMapping(prev => ({
            ...prev,
            [erpColumn]: targetColumn
        }));
    };

    const isAllFieldsMapped = () => {
        if (!analysis) return false;
        const mappedTargets = new Set(Object.values(columnMapping));
        return analysis.required_fields.every(field => mappedTargets.has(field));
    };

    return (
        <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
            <DialogTitle>Import Inventory Data</DialogTitle>
            <DialogContent>
                <Box sx={{ mt: 2 }}>
                    <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
                        {steps.map((label) => (
                            <Step key={label}>
                                <StepLabel>{label}</StepLabel>
                            </Step>
                        ))}
                    </Stepper>

                    {/* Step 1: File Upload */}
                    {activeStep === 0 && (
                        <Box>
                            <Typography variant="body1" gutterBottom>
                                Upload your CSV file from any ERP system. We'll automatically detect and map your columns.
                            </Typography>
                            <Alert severity="info" sx={{ mt: 2, mb: 2 }}>
                                <Typography variant="body2">
                                    <strong>Supported formats:</strong> Any CSV with product/item data, lot/batch numbers, expiration dates, quantities, and dates.
                                </Typography>
                            </Alert>
                            <Box sx={{ mt: 2 }}>
                                <input
                                    accept=".csv"
                                    style={{ display: 'none' }}
                                    id="csv-file-upload"
                                    type="file"
                                    onChange={handleFileChange}
                                />
                                <label htmlFor="csv-file-upload">
                                    <Button variant="contained" component="span">
                                        Select CSV File
                                    </Button>
                                </label>
                                {file && (
                                    <Typography variant="body2" sx={{ mt: 1 }}>
                                        Selected file: {file.name}
                                    </Typography>
                                )}
                            </Box>

                            {/* Legacy import option */}
                            <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                                <Typography variant="body2" color="text.secondary" gutterBottom>
                                    <strong>Already have the exact format?</strong> If your CSV has these exact columns:
                                </Typography>
                                <Typography variant="body2" color="text.secondary" component="pre" sx={{ fontSize: '0.75rem' }}>
                                    Item_No, Lot_No, Expiration_Date, Quantity, Last_Movement_Date
                                </Typography>
                                <Button
                                    size="small"
                                    onClick={handleTryLegacyImport}
                                    disabled={!file || legacyMutation.isLoading}
                                    sx={{ mt: 1 }}
                                >
                                    {legacyMutation.isLoading ? <CircularProgress size={16} /> : 'Quick Import'}
                                </Button>
                            </Box>
                        </Box>
                    )}

                    {/* Step 2: Column Mapping */}
                    {activeStep === 1 && analysis && (
                        <Box>
                            <Typography variant="h6" gutterBottom>
                                Review Column Mapping
                            </Typography>
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                                Found {analysis.total_rows} rows. Please verify the column mappings below:
                            </Typography>

                            <Grid container spacing={2} sx={{ mt: 2 }}>
                                {analysis.columns.map((col) => (
                                    <Grid item xs={12} sm={6} key={col.name}>
                                        <FormControl fullWidth size="small">
                                            <InputLabel>Map "{col.name}" to</InputLabel>
                                            <Select
                                                value={columnMapping[col.name] || ''}
                                                onChange={(e) => handleMappingChange(col.name, e.target.value)}
                                                label={`Map "${col.name}" to`}
                                            >
                                                <MenuItem value="">
                                                    <em>Skip this column</em>
                                                </MenuItem>
                                                {analysis.required_fields.map((field) => (
                                                    <MenuItem key={field} value={field}>
                                                        {field}
                                                    </MenuItem>
                                                ))}
                                            </Select>
                                            <Typography variant="caption" color="text.secondary">
                                                Sample: {col.sample_values.slice(0, 2).join(', ')}
                                            </Typography>
                                        </FormControl>
                                    </Grid>
                                ))}
                            </Grid>

                            <Box sx={{ mt: 3 }}>
                                <Typography variant="body2" gutterBottom>
                                    Required fields status:
                                </Typography>
                                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                                    {analysis.required_fields.map((field) => {
                                        const isMapped = Object.values(columnMapping).includes(field);
                                        return (
                                            <Chip
                                                key={field}
                                                label={field}
                                                color={isMapped ? 'success' : 'default'}
                                                variant={isMapped ? 'filled' : 'outlined'}
                                                size="small"
                                            />
                                        );
                                    })}
                                </Box>
                            </Box>
                        </Box>
                    )}

                    {/* Error Messages */}
                    {(analyzeMutation.isError || importMutation.isError || legacyMutation.isError) && (
                        <Alert severity="error" sx={{ mt: 2 }}>
                            {analyzeMutation.error && `Analysis error: ${(analyzeMutation.error as Error)?.message}`}
                            {importMutation.error && `Import error: ${(importMutation.error as Error)?.message}`}
                            {legacyMutation.error && `Import error: ${(legacyMutation.error as Error)?.message}`}
                        </Alert>
                    )}

                    {/* Success Messages */}
                    {(importMutation.isSuccess || legacyMutation.isSuccess) && (
                        <Alert severity="success" sx={{ mt: 2 }}>
                            <Typography variant="h6" gutterBottom>
                                🎉 Import Successful!
                            </Typography>
                            <Typography variant="body2">
                                Your CSV file has been processed and imported successfully.
                                {legacyMutation.data?.imported_count && (
                                    <><br />✅ Imported {legacyMutation.data.imported_count} items</>
                                )}
                                {legacyMutation.data?.error_count > 0 && (
                                    <><br />⚠️ {legacyMutation.data.error_count} rows had minor issues but were handled</>
                                )}
                                <br />📊 Dashboard will refresh automatically to show your new data.
                            </Typography>
                        </Alert>
                    )}
                </Box>
            </DialogContent>
            <DialogActions>
                <Button onClick={handleClose}>Cancel</Button>

                {activeStep === 0 && (
                    <Button
                        onClick={handleAnalyze}
                        disabled={!file || analyzeMutation.isLoading}
                        variant="contained"
                    >
                        {analyzeMutation.isLoading ? <CircularProgress size={24} /> : 'Analyze File'}
                    </Button>
                )}

                {activeStep === 1 && (
                    <>
                        <Button onClick={() => setActiveStep(0)}>
                            Back
                        </Button>
                        <Button
                            onClick={handleImportWithMapping}
                            disabled={!isAllFieldsMapped() || importMutation.isLoading}
                            variant="contained"
                        >
                            {importMutation.isLoading ? <CircularProgress size={24} /> : 'Import Data'}
                        </Button>
                    </>
                )}
            </DialogActions>
        </Dialog>
    );
};