import React, { useState } from 'react';
import {
    <PERSON>alog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    Box,
    Typography,
    Alert,
    CircularProgress,
} from '@mui/material';
import { useMutation, useQueryClient } from 'react-query';
import { importInventoryData } from '../services/api';

interface ImportDialogProps {
    open: boolean;
    onClose: () => void;
}

export const ImportDialog: React.FC<ImportDialogProps> = ({ open, onClose }) => {
    const [file, setFile] = useState<File | null>(null);
    const queryClient = useQueryClient();

    const mutation = useMutation(importInventoryData, {
        onSuccess: () => {
            queryClient.invalidateQueries(['heatmap', 'distribution', 'wasteRisk']);
            onClose();
        },
    });

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            setFile(event.target.files[0]);
        }
    };

    const handleUpload = async () => {
        if (file) {
            mutation.mutate(file);
        }
    };

    return (
        <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
            <DialogTitle>Import Inventory Data</DialogTitle>
            <DialogContent>
                <Box sx={{ mt: 2 }}>
                    <Typography variant="body1" gutterBottom>
                        Upload a CSV file with the following columns:
                    </Typography>
                    <Typography variant="body2" color="text.secondary" component="pre" sx={{ mt: 1 }}>
                        Item_No, Lot_No, Expiration_Date, Quantity, Last_Movement_Date
                    </Typography>
                    <Box sx={{ mt: 2 }}>
                        <input
                            accept=".csv"
                            style={{ display: 'none' }}
                            id="csv-file-upload"
                            type="file"
                            onChange={handleFileChange}
                        />
                        <label htmlFor="csv-file-upload">
                            <Button variant="contained" component="span">
                                Select CSV File
                            </Button>
                        </label>
                        {file && (
                            <Typography variant="body2" sx={{ mt: 1 }}>
                                Selected file: {file.name}
                            </Typography>
                        )}
                    </Box>
                    {mutation.isError && (
                        <Alert severity="error" sx={{ mt: 2 }}>
                            Error uploading file: {(mutation.error as Error)?.message}
                        </Alert>
                    )}
                    {mutation.isSuccess && (
                        <Alert severity="success" sx={{ mt: 2 }}>
                            File uploaded successfully!
                        </Alert>
                    )}
                </Box>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose}>Cancel</Button>
                <Button
                    onClick={handleUpload}
                    disabled={!file || mutation.isLoading}
                    variant="contained"
                >
                    {mutation.isLoading ? <CircularProgress size={24} /> : 'Upload'}
                </Button>
            </DialogActions>
        </Dialog>
    );
}; 