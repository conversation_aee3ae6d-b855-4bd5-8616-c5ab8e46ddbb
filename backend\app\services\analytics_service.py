from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func
from app.models.product import Product, Lot, InventoryItem
import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression

def calculate_shelf_life_heatmap(db: Session) -> List[Dict[str, Any]]:
    """Calculate shelf life heatmap data grouped by category and status."""
    from app.models.product import ProductCategory

    # Get all inventory items with their products and lots
    inventory_query = db.query(InventoryItem).join(Product).join(Lot).all()

    # Group data by category and status
    category_status_data = {}

    for item in inventory_query:
        try:
            days_to_expiry = (item.lot.expiration_date - datetime.now()).days
            category = item.product.category.value if hasattr(item.product.category, 'value') else str(item.product.category)

            # Determine status based on days to expiry
            if days_to_expiry <= 0:
                status = "Expired"
                risk_value = 90 + min(10, abs(days_to_expiry))  # 90-100
            elif days_to_expiry <= 3:
                status = "Critical"
                risk_value = 70 + (3 - days_to_expiry) * 6.67  # 70-90
            elif days_to_expiry <= 7:
                status = "Near Expiry"
                risk_value = 40 + (7 - days_to_expiry) * 7.5  # 40-70
            else:
                status = "Fresh"
                risk_value = max(10, 40 - (days_to_expiry - 7) * 2)  # 10-40

            # Create key for grouping
            key = (category, status)

            if key not in category_status_data:
                category_status_data[key] = {
                    "total_items": 0,
                    "total_quantity": 0,
                    "risk_values": [],
                    "avg_days_to_expiry": []
                }

            category_status_data[key]["total_items"] += 1
            category_status_data[key]["total_quantity"] += item.quantity
            category_status_data[key]["risk_values"].append(risk_value)
            category_status_data[key]["avg_days_to_expiry"].append(days_to_expiry)

        except Exception as e:
            print(f"Error processing item {item.id}: {e}")
            continue

    # Convert to heatmap format
    heatmap_data = []
    for (category, status), data in category_status_data.items():
        avg_risk = sum(data["risk_values"]) / len(data["risk_values"]) if data["risk_values"] else 0
        avg_days = sum(data["avg_days_to_expiry"]) / len(data["avg_days_to_expiry"]) if data["avg_days_to_expiry"] else 0

        heatmap_data.append({
            "x": category,
            "y": status,
            "value": round(avg_risk, 1),
            "items": data["total_items"],
            "quantity": data["total_quantity"],
            "avg_days_to_expiry": round(avg_days, 1)
        })

    # If no data, return empty array - don't show fake data
    if not heatmap_data:
        print("⚠️  No inventory data found for heatmap")
        return []

    return heatmap_data

def get_distribution_suggestions(db: Session) -> List[Dict[str, Any]]:
    """Generate actionable distribution suggestions based on expiry dates and inventory status."""
    # Get all inventory items with their lots and products
    inventory = db.query(InventoryItem).join(Product).join(Lot).all()
    suggestions = []

    for item in inventory:
        try:
            days_to_expiry = (item.lot.expiration_date - datetime.now()).days

            # Calculate urgency score (0-100)
            urgency_score = 0
            action_type = "monitor"
            priority = "Low"

            # Factor 1: Days to expiry (50% weight)
            if days_to_expiry <= 0:
                urgency_score += 50
                action_type = "dispose"
                priority = "Critical"
            elif days_to_expiry <= 1:
                urgency_score += 45
                action_type = "clearance_sale"
                priority = "Critical"
            elif days_to_expiry <= 3:
                urgency_score += 35
                action_type = "discount"
                priority = "High"
            elif days_to_expiry <= 7:
                urgency_score += 25
                action_type = "promote"
                priority = "Medium"
            else:
                urgency_score += max(0, (30 - days_to_expiry) * 0.5)

            # Factor 2: Temperature deviation (25% weight)
            temp_deviation = abs(item.current_temp - item.product.optimal_storage_temp)
            if temp_deviation > 5:
                urgency_score += 25
                if action_type == "monitor":
                    action_type = "relocate"
            elif temp_deviation > 2:
                urgency_score += 15

            # Factor 3: Quantity impact (25% weight)
            if item.quantity > 50:
                urgency_score += 25  # High quantity = high impact
            elif item.quantity > 20:
                urgency_score += 15
            elif item.quantity > 5:
                urgency_score += 10

            # Generate specific action recommendations
            if urgency_score >= 80:
                action = f"URGENT: {action_type.replace('_', ' ').title()} immediately"
                priority = "Critical"
            elif urgency_score >= 60:
                action = f"HIGH: {action_type.replace('_', ' ').title()} within 24 hours"
                priority = "High"
            elif urgency_score >= 40:
                action = f"MEDIUM: {action_type.replace('_', ' ').title()} within 3 days"
                priority = "Medium"
            else:
                action = f"LOW: Continue monitoring"
                priority = "Low"

            # Calculate potential loss value (simplified)
            estimated_value = item.quantity * 5.0  # Assume $5 per unit
            potential_loss = estimated_value if days_to_expiry <= 0 else estimated_value * (max(0, 7 - days_to_expiry) / 7)

            suggestions.append({
                "id": item.id,
                "product_name": item.product.name,
                "sku": item.product.sku,
                "location": item.location,
                "quantity": item.quantity,
                "days_to_expiry": days_to_expiry,
                "urgency_score": round(urgency_score, 1),
                "priority": priority,
                "action": action,
                "action_type": action_type,
                "temp_deviation": round(temp_deviation, 1),
                "estimated_value": round(estimated_value, 2),
                "potential_loss": round(potential_loss, 2),
                "expiry_date": item.lot.expiration_date.strftime("%Y-%m-%d")
            })

        except Exception as e:
            print(f"Error processing distribution suggestion for item {item.id}: {e}")
            continue

    # Sort by urgency score, then by potential loss
    suggestions.sort(key=lambda x: (x["urgency_score"], x["potential_loss"]), reverse=True)

    # If no data, return empty array
    if not suggestions:
        print("⚠️  No inventory data found for distribution suggestions")
        return []

    # Limit to top 20 most urgent items
    return suggestions[:20]

def analyze_waste_risk(db: Session) -> List[Dict[str, Any]]:
    """Analyze comprehensive waste risk with financial impact and recommendations."""
    # Get all inventory items with their lots and products
    inventory = db.query(InventoryItem).join(Product).join(Lot).all()
    waste_risks = []

    for item in inventory:
        try:
            days_to_expiry = (item.lot.expiration_date - datetime.now()).days

            # Calculate multiple risk factors
            risk_factors = {
                "expiry_risk": 0,
                "quantity_risk": 0,
                "temperature_risk": 0,
                "category_risk": 0
            }

            # Factor 1: Expiry Risk (40% weight)
            if days_to_expiry <= 0:
                risk_factors["expiry_risk"] = 40
            elif days_to_expiry <= 1:
                risk_factors["expiry_risk"] = 35
            elif days_to_expiry <= 3:
                risk_factors["expiry_risk"] = 25
            elif days_to_expiry <= 7:
                risk_factors["expiry_risk"] = 15
            elif days_to_expiry <= 14:
                risk_factors["expiry_risk"] = 8
            else:
                risk_factors["expiry_risk"] = max(0, (30 - days_to_expiry) * 0.3)

            # Factor 2: Quantity Risk (30% weight) - higher quantity = higher risk
            if item.quantity > 100:
                risk_factors["quantity_risk"] = 30
            elif item.quantity > 50:
                risk_factors["quantity_risk"] = 20
            elif item.quantity > 20:
                risk_factors["quantity_risk"] = 15
            elif item.quantity > 10:
                risk_factors["quantity_risk"] = 10
            else:
                risk_factors["quantity_risk"] = 5

            # Factor 3: Temperature Risk (20% weight)
            temp_deviation = abs(item.current_temp - item.product.optimal_storage_temp)
            if temp_deviation > 10:
                risk_factors["temperature_risk"] = 20
            elif temp_deviation > 5:
                risk_factors["temperature_risk"] = 15
            elif temp_deviation > 2:
                risk_factors["temperature_risk"] = 10
            else:
                risk_factors["temperature_risk"] = 5

            # Factor 4: Category Risk (10% weight) - some categories spoil faster
            category = item.product.category.value if hasattr(item.product.category, 'value') else str(item.product.category)
            category_risk_map = {
                "DAIRY": 10,
                "MEAT": 10,
                "PRODUCE": 8,
                "BAKERY": 7,
                "FROZEN": 5,
                "OTHER": 6
            }
            risk_factors["category_risk"] = category_risk_map.get(category.upper(), 6)

            # Calculate total waste risk
            total_waste_risk = sum(risk_factors.values())

            # Determine status and recommendations
            if total_waste_risk >= 80:
                status = "Critical Risk"
                recommendation = "Immediate action required - dispose or emergency sale"
                color = "red"
            elif total_waste_risk >= 60:
                status = "High Risk"
                recommendation = "Urgent discount or promotion needed"
                color = "orange"
            elif total_waste_risk >= 40:
                status = "Medium Risk"
                recommendation = "Monitor closely, consider promotion"
                color = "yellow"
            elif total_waste_risk >= 20:
                status = "Low Risk"
                recommendation = "Continue normal operations"
                color = "lightgreen"
            else:
                status = "Minimal Risk"
                recommendation = "Optimal condition"
                color = "green"

            # Calculate financial impact
            unit_cost = 5.0  # Simplified - assume $5 per unit
            total_value = item.quantity * unit_cost

            # Waste probability based on risk score
            waste_probability = min(1.0, total_waste_risk / 100)
            expected_waste_value = total_value * waste_probability

            # Calculate days until action needed
            if days_to_expiry <= 0:
                action_deadline = "Overdue"
            elif days_to_expiry <= 1:
                action_deadline = "Today"
            elif days_to_expiry <= 3:
                action_deadline = f"{days_to_expiry} days"
            else:
                action_deadline = f"{days_to_expiry} days"

            waste_risks.append({
                "id": item.id,
                "product_name": item.product.name,
                "sku": item.product.sku,
                "category": category,
                "location": item.location,
                "quantity": item.quantity,
                "days_to_expiry": days_to_expiry,
                "expiry_date": item.lot.expiration_date.strftime("%Y-%m-%d"),
                "waste_risk": round(total_waste_risk, 1),
                "status": status,
                "recommendation": recommendation,
                "color": color,
                "risk_factors": risk_factors,
                "temp_deviation": round(temp_deviation, 1),
                "current_temp": item.current_temp,
                "optimal_temp": item.product.optimal_storage_temp,
                "total_value": round(total_value, 2),
                "expected_waste_value": round(expected_waste_value, 2),
                "waste_probability": round(waste_probability * 100, 1),
                "action_deadline": action_deadline
            })

        except Exception as e:
            print(f"Error analyzing waste risk for item {item.id}: {e}")
            continue

    # Sort by waste risk, then by expected waste value
    waste_risks.sort(key=lambda x: (x["waste_risk"], x["expected_waste_value"]), reverse=True)

    # If no data, return empty array
    if not waste_risks:
        print("⚠️  No inventory data found for waste risk analysis")
        return []

    # Limit to top 25 highest risk items
    return waste_risks[:25]