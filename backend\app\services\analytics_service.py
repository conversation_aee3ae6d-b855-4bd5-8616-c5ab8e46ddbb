from datetime import datetime, timedelta
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func
from app.models.product import Product, Lot, InventoryItem
import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression

def calculate_shelf_life_heatmap(db: Session) -> List[Dict[str, Any]]:
    """Calculate shelf life status for all inventory items."""
    inventory = db.query(InventoryItem).all()
    heatmap_data = []
    
    for item in inventory:
        days_to_expiry = (item.lot.expiration_date - datetime.utcnow()).days
        
        # Calculate risk level (0-100)
        if days_to_expiry <= 0:
            risk_level = 100  # Expired
        elif days_to_expiry <= 7:
            risk_level = 80  # Critical
        elif days_to_expiry <= 14:
            risk_level = 60  # High risk
        elif days_to_expiry <= 30:
            risk_level = 40  # Medium risk
        else:
            risk_level = 20  # Low risk
            
        heatmap_data.append({
            "id": item.id,
            "product_name": item.product.name,
            "location": item.location,
            "quantity": item.quantity,
            "days_to_expiry": days_to_expiry,
            "risk_level": risk_level,
            "current_temp": item.current_temp,
            "optimal_temp": item.product.optimal_storage_temp
        })
    
    return heatmap_data

def get_distribution_suggestions(db: Session) -> List[Dict[str, Any]]:
    """Generate distribution suggestions based on expiry dates and sales velocity."""
    # Get all inventory items with their lots and products
    inventory = db.query(InventoryItem).all()
    suggestions = []
    
    for item in inventory:
        days_to_expiry = (item.lot.expiration_date - datetime.utcnow()).days
        
        # Calculate urgency score (0-100)
        urgency_score = 0
        
        # Factor 1: Days to expiry (40% weight)
        if days_to_expiry <= 0:
            urgency_score += 40
        else:
            urgency_score += min(40, (30 - days_to_expiry) * 1.33)
            
        # Factor 2: Temperature deviation (30% weight)
        temp_deviation = abs(item.current_temp - item.product.optimal_storage_temp)
        urgency_score += min(30, temp_deviation * 5)
        
        # Factor 3: Quantity (30% weight)
        if item.quantity > 0:
            urgency_score += min(30, item.quantity * 0.1)
            
        suggestions.append({
            "id": item.id,
            "product_name": item.product.name,
            "location": item.location,
            "quantity": item.quantity,
            "days_to_expiry": days_to_expiry,
            "urgency_score": urgency_score,
            "suggestion": "Prioritize" if urgency_score > 70 else "Normal" if urgency_score > 40 else "Low Priority"
        })
    
    # Sort by urgency score
    suggestions.sort(key=lambda x: x["urgency_score"], reverse=True)
    return suggestions

def analyze_waste_risk(db: Session) -> List[Dict[str, Any]]:
    """Analyze waste risk based on sales velocity and expiry dates."""
    # Get all inventory items
    inventory = db.query(InventoryItem).all()
    waste_risks = []
    
    for item in inventory:
        days_to_expiry = (item.lot.expiration_date - datetime.utcnow()).days
        
        # Calculate daily consumption rate (simplified)
        # In a real system, this would use historical sales data
        daily_consumption = item.quantity / max(1, days_to_expiry)
        
        # Calculate waste risk
        if days_to_expiry <= 0:
            waste_risk = 100
        elif daily_consumption <= 0:
            waste_risk = 90
        else:
            days_needed = item.quantity / daily_consumption
            waste_risk = min(100, max(0, (days_needed - days_to_expiry) * 10))
            
        waste_risks.append({
            "id": item.id,
            "product_name": item.product.name,
            "location": item.location,
            "quantity": item.quantity,
            "days_to_expiry": days_to_expiry,
            "daily_consumption": daily_consumption,
            "waste_risk": waste_risk,
            "status": "High Risk" if waste_risk > 70 else "Medium Risk" if waste_risk > 40 else "Low Risk"
        })
    
    # Sort by waste risk
    waste_risks.sort(key=lambda x: x["waste_risk"], reverse=True)
    return waste_risks 