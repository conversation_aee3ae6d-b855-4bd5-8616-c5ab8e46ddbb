#!/bin/bash

echo "Starting Smart Shelf Life Optimizer..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "Docker is not running. Please start Docker daemon."
    exit 1
fi

# Build and start the containers
echo "Building and starting containers..."
docker-compose up --build -d

# Wait for services to start
echo "Waiting for services to start..."
sleep 10

# Open the application in the default browser
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    open http://localhost
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    xdg-open http://localhost
fi

echo "Application is running!"
echo "Frontend: http://localhost"
echo "Backend API: http://localhost:8000"
echo "API Documentation: http://localhost:8000/docs"
echo
echo "Press Ctrl+C to stop the application"
echo

# Show logs
docker-compose logs -f 