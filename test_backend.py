#!/usr/bin/env python3
import requests
import time

def test_backend():
    print("Testing backend connection...")
    
    # Wait for backend to be ready
    for i in range(10):
        try:
            response = requests.get("http://localhost:8000/", timeout=5)
            if response.status_code == 200:
                print("✅ Backend is running!")
                print(f"Response: {response.json()}")
                return True
        except Exception as e:
            print(f"Attempt {i+1}: Backend not ready yet... ({e})")
            time.sleep(2)
    
    print("❌ Backend failed to start")
    return False

def test_erp_import():
    print("\nTesting ERP CSV import...")
    
    # Create test CSV with ERP format
    csv_content = """No.,Description,Quantity Expired,Case Units on Hand,Quantity on Hand,Base Unit of Measure,Substitutes Exist,Assembly BOM,Cost is Adjusted,Unit Cost,Unit Price
ITEM001,Sample Product 1,0,10,100,EA,No,No,No,5.00,10.00
ITEM002,Sample Product 2,0,5,50,EA,No,No,No,3.00,6.00"""
    
    with open('test_erp.csv', 'w') as f:
        f.write(csv_content)
    
    try:
        with open('test_erp.csv', 'rb') as f:
            files = {'file': f}
            response = requests.post("http://localhost:8000/api/v1/inventory/import-csv", files=files, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ ERP import successful!")
            return True
        else:
            print("❌ ERP import failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing import: {e}")
        return False

if __name__ == "__main__":
    if test_backend():
        test_erp_import()
    else:
        print("Cannot test import - backend not running")
