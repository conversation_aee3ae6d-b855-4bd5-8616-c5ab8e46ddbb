import React from 'react';
import {
    List,
    ListItem,
    ListItemText,
    Typography,
    Box,
    Chip,
    CircularProgress,
} from '@mui/material';
import { useQuery } from 'react-query';
import { getDistributionSuggestions } from '../services/api';
import { DistributionSuggestion } from '../types';

export const DistributionList: React.FC = () => {
    const { data, isLoading, error } = useQuery<DistributionSuggestion[]>(
        'distribution',
        getDistributionSuggestions
    );

    if (isLoading) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return <div>Error loading distribution suggestions</div>;
    }

    if (!data) {
        return <div>No data available</div>;
    }

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'Critical':
                return 'error' as const;
            case 'High':
                return 'warning' as const;
            case 'Medium':
                return 'info' as const;
            default:
                return 'success' as const;
        }
    };

    return (
        <List sx={{ width: '100%', bgcolor: 'background.paper', overflow: 'auto', maxHeight: 300 }}>
            {data.map((item) => (
                <ListItem
                    key={item.id}
                    alignItems="flex-start"
                    sx={{
                        borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                        '&:last-child': {
                            borderBottom: 'none',
                        },
                    }}
                >
                    <ListItemText
                        primary={
                            <Box display="flex" alignItems="center" gap={1}>
                                <Typography variant="subtitle1">{item.product_name}</Typography>
                                <Chip
                                    label={item.priority || 'Medium'}
                                    color={getPriorityColor(item.priority || 'Medium')}
                                    size="small"
                                />
                            </Box>
                        }
                        secondary={
                            <React.Fragment>
                                <Typography
                                    component="span"
                                    variant="body2"
                                    color="text.primary"
                                    display="block"
                                >
                                    📍 {item.location} • 📦 {item.quantity} units • ⏰ {item.days_to_expiry} days
                                </Typography>
                                <Typography
                                    component="span"
                                    variant="body2"
                                    color={item.priority === 'Critical' ? 'error.main' : 'text.primary'}
                                    display="block"
                                    sx={{ fontWeight: 'medium', mt: 0.5 }}
                                >
                                    {item.action || `${item.suggestion || 'Monitor'} - Score: ${item.urgency_score}`}
                                </Typography>
                                {item.potential_loss && item.potential_loss > 0 && (
                                    <Typography
                                        component="span"
                                        variant="caption"
                                        color="error.main"
                                        display="block"
                                    >
                                        💰 Potential loss: ${item.potential_loss}
                                    </Typography>
                                )}
                            </React.Fragment>
                        }
                    />
                </ListItem>
            ))}
        </List>
    );
};