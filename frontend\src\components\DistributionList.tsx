import React from 'react';
import {
    List,
    ListItem,
    ListItemText,
    Typography,
    Box,
    Chip,
    CircularProgress,
} from '@mui/material';
import { useQuery } from 'react-query';
import { getDistributionSuggestions } from '../services/api';
import { DistributionSuggestion } from '../types';

export const DistributionList: React.FC = () => {
    const { data, isLoading, error } = useQuery<DistributionSuggestion[]>(
        'distribution',
        getDistributionSuggestions
    );

    if (isLoading) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return <div>Error loading distribution suggestions</div>;
    }

    if (!data) {
        return <div>No data available</div>;
    }

    const getChipColor = (suggestion: string) => {
        switch (suggestion) {
            case 'Prioritize':
                return 'error';
            case 'Normal':
                return 'warning';
            case 'Low Priority':
                return 'success';
            default:
                return 'default';
        }
    };

    return (
        <List sx={{ width: '100%', bgcolor: 'background.paper', overflow: 'auto', maxHeight: 300 }}>
            {data.map((item) => (
                <ListItem
                    key={item.id}
                    alignItems="flex-start"
                    sx={{
                        borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                        '&:last-child': {
                            borderBottom: 'none',
                        },
                    }}
                >
                    <ListItemText
                        primary={
                            <Box display="flex" alignItems="center" gap={1}>
                                <Typography variant="subtitle1">{item.product_name}</Typography>
                                <Chip
                                    label={item.suggestion}
                                    color={getChipColor(item.suggestion)}
                                    size="small"
                                />
                            </Box>
                        }
                        secondary={
                            <React.Fragment>
                                <Typography
                                    component="span"
                                    variant="body2"
                                    color="text.primary"
                                    display="block"
                                >
                                    Location: {item.location}
                                </Typography>
                                <Typography
                                    component="span"
                                    variant="body2"
                                    color="text.primary"
                                    display="block"
                                >
                                    Quantity: {item.quantity}
                                </Typography>
                                <Typography
                                    component="span"
                                    variant="body2"
                                    color="text.primary"
                                    display="block"
                                >
                                    Days to Expiry: {item.days_to_expiry}
                                </Typography>
                                <Typography
                                    component="span"
                                    variant="body2"
                                    color="text.primary"
                                    display="block"
                                >
                                    Urgency Score: {item.urgency_score}
                                </Typography>
                            </React.Fragment>
                        }
                    />
                </ListItem>
            ))}
        </List>
    );
}; 