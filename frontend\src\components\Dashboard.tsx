import React from 'react';
import {
    Box,
    Container,
    Grid,
    Paper,
    Typography,
    AppBar,
    Toolbar,
    IconButton,
    Drawer,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    useTheme,
    Button,
} from '@mui/material';
import {
    Menu as MenuIcon,
    Dashboard as DashboardIcon,
    Warning as WarningIcon,
    TrendingUp as TrendingUpIcon,
    Upload as UploadIcon,
    Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useQueryClient } from 'react-query';
import { HeatmapChart } from './HeatmapChart';
import { DistributionList } from './DistributionList';
import { WasteRiskList } from './WasteRiskList';
import { ImportDialog } from './ImportDialog';
import { generateSampleData } from '../services/api';

const drawerWidth = 240;

export const Dashboard: React.FC = () => {
    const theme = useTheme();
    const [mobileOpen, setMobileOpen] = React.useState(false);
    const [importOpen, setImportOpen] = React.useState(false);
    const queryClient = useQueryClient();

    const handleDrawerToggle = () => {
        setMobileOpen(!mobileOpen);
    };

    const handleGenerateData = async () => {
        try {
            const data = await generateSampleData();
            // Update all queries with the new data
            queryClient.setQueryData('heatmap', data.heatmap);
            queryClient.setQueryData('distribution', data.distribution_suggestions);
            queryClient.setQueryData('wasteRisk', data.waste_risk_analysis);
        } catch (error) {
            console.error('Error generating sample data:', error);
        }
    };

    const drawer = (
        <div>
            <Toolbar>
                <Typography variant="h6" noWrap component="div">
                    Smart Shelf Life
                </Typography>
            </Toolbar>
            <List>
                <ListItem>
                    <ListItemIcon>
                        <DashboardIcon />
                    </ListItemIcon>
                    <ListItemText primary="Dashboard" />
                </ListItem>
                <ListItem onClick={() => setImportOpen(true)}>
                    <ListItemIcon>
                        <UploadIcon />
                    </ListItemIcon>
                    <ListItemText primary="Import Data" />
                </ListItem>
                <ListItem onClick={handleGenerateData}>
                    <ListItemIcon>
                        <RefreshIcon />
                    </ListItemIcon>
                    <ListItemText primary="Generate Fun Data" />
                </ListItem>
            </List>
        </div>
    );

    return (
        <Box sx={{ display: 'flex' }}>
            <AppBar
                position="fixed"
                sx={{
                    width: { sm: `calc(100% - ${drawerWidth}px)` },
                    ml: { sm: `${drawerWidth}px` },
                }}
            >
                <Toolbar>
                    <IconButton
                        color="inherit"
                        aria-label="open drawer"
                        edge="start"
                        onClick={handleDrawerToggle}
                        sx={{ mr: 2, display: { sm: 'none' } }}
                    >
                        <MenuIcon />
                    </IconButton>
                    <Typography variant="h6" noWrap component="div">
                        Smart Shelf Life Optimizer
                    </Typography>
                </Toolbar>
            </AppBar>
            <Box
                component="nav"
                sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
            >
                <Drawer
                    variant="temporary"
                    open={mobileOpen}
                    onClose={handleDrawerToggle}
                    ModalProps={{
                        keepMounted: true,
                    }}
                    sx={{
                        display: { xs: 'block', sm: 'none' },
                        '& .MuiDrawer-paper': {
                            boxSizing: 'border-box',
                            width: drawerWidth,
                        },
                    }}
                >
                    {drawer}
                </Drawer>
                <Drawer
                    variant="permanent"
                    sx={{
                        display: { xs: 'none', sm: 'block' },
                        '& .MuiDrawer-paper': {
                            boxSizing: 'border-box',
                            width: drawerWidth,
                        },
                    }}
                    open
                >
                    {drawer}
                </Drawer>
            </Box>
            <Box
                component="main"
                sx={{
                    flexGrow: 1,
                    p: 3,
                    width: { sm: `calc(100% - ${drawerWidth}px)` },
                }}
            >
                <Toolbar />
                <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                        {/* Heatmap Chart */}
                        <Paper
                            sx={{
                                p: 2,
                                display: 'flex',
                                flexDirection: 'column',
                                height: 400,
                            }}
                        >
                            <Typography component="h2" variant="h6" color="primary" gutterBottom>
                                Shelf Life Heatmap
                            </Typography>
                            <HeatmapChart />
                        </Paper>
                        
                        <Box sx={{ display: 'flex', gap: 3, flexDirection: { xs: 'column', md: 'row' } }}>
                            {/* Distribution Suggestions */}
                            <Paper
                                sx={{
                                    p: 2,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    height: 400,
                                    flex: 1,
                                }}
                            >
                                <Typography component="h2" variant="h6" color="primary" gutterBottom>
                                    Distribution Suggestions
                                </Typography>
                                <DistributionList />
                            </Paper>
                            
                            {/* Waste Risk Analysis */}
                            <Paper
                                sx={{
                                    p: 2,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    height: 400,
                                    flex: 1,
                                }}
                            >
                                <Typography component="h2" variant="h6" color="primary" gutterBottom>
                                    Waste Risk Analysis
                                </Typography>
                                <WasteRiskList />
                            </Paper>
                        </Box>
                    </Box>
                </Container>
            </Box>
            <ImportDialog open={importOpen} onClose={() => setImportOpen(false)} />
        </Box>
    );
}; 