#!/usr/bin/env python3
"""
Quick test to verify ERP CSV import functionality
"""
import sys
import os
sys.path.append('backend')

# Mock the database and models for testing
class MockDB:
    def query(self, model):
        return MockQuery()
    def add(self, item):
        pass
    def flush(self):
        pass
    def commit(self):
        pass
    def rollback(self):
        pass

class MockQuery:
    def filter(self, *args):
        return self
    def first(self):
        return None

class MockProduct:
    def __init__(self, **kwargs):
        self.id = 1
        for k, v in kwargs.items():
            setattr(self, k, v)

class MockLot:
    def __init__(self, **kwargs):
        self.id = 1
        for k, v in kwargs.items():
            setattr(self, k, v)

class MockInventoryItem:
    def __init__(self, **kwargs):
        for k, v in kwargs.items():
            setattr(self, k, v)

class MockProductCategory:
    OTHER = "OTHER"

# Mock the models
sys.modules['app.models.product'] = type('MockModule', (), {
    'Product': MockProduct,
    'Lot': MockLot,
    'InventoryItem': MockInventoryItem,
    'ProductCategory': MockProductCategory
})()

# Now test the import function
def test_erp_import():
    print("Testing ERP CSV import logic...")
    
    # Your ERP CSV format
    csv_data = """No.,Description,Quantity Expired,Case Units on Hand,Quantity on Hand,Base Unit of Measure,Substitutes Exist,Assembly BOM,Cost is Adjusted,Unit Cost,Unit Price
ITEM001,Sample Product 1,0,10,100,EA,No,No,No,5.00,10.00
ITEM002,Sample Product 2,0,5,50,EA,No,No,No,3.00,6.00"""
    
    try:
        # Import the function
        from backend.app.services.import_service import import_inventory_data
        
        # Test with mock database
        db = MockDB()
        result = import_inventory_data(db, csv_data)
        
        print(f"Result: {result}")
        
        if result.get("success"):
            print("✅ ERP import logic works!")
            return True
        else:
            print(f"❌ Import failed: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing import: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_erp_import()
